-- Создание базы данных для Discord групп
-- Этот файл автоматически выполнится при первом запуске PostgreSQL

-- Создание таблицы пользователей
CREATE TABLE IF NOT EXISTS users (
    user_id BIGINT PRIMARY KEY,
    username VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    avatar_url TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    last_seen TIMESTAMP DEFAULT NOW()
);

-- Создание таблицы групп
CREATE TABLE IF NOT EXISTS groups (
    group_id BIGINT PRIMARY KEY,
    channel_id BIGINT UNIQUE NOT NULL, -- ID канала Discord для навигации
    name VARCHAR(255) NOT NULL,
    description TEXT,
    owner_id BIGINT,
    member_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Создание таблицы связей пользователь-группа
CREATE TABLE IF NOT EXISTS user_groups (
    user_id BIGINT NOT NULL,
    group_id BIGINT NOT NULL,
    joined_at TIMESTAMP DEFAULT NOW(),
    role VARCHAR(50) DEFAULT 'member', -- member, admin, owner
    is_active BOOLEAN DEFAULT TRUE,
    PRIMARY KEY (user_id, group_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES groups(group_id) ON DELETE CASCADE
);

-- Создание индексов для быстрого поиска
CREATE INDEX IF NOT EXISTS idx_user_groups_user_id ON user_groups(user_id);
CREATE INDEX IF NOT EXISTS idx_user_groups_group_id ON user_groups(group_id);
CREATE INDEX IF NOT EXISTS idx_user_groups_active ON user_groups(is_active);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_groups_name ON groups USING gin(to_tsvector('russian', name));
CREATE INDEX IF NOT EXISTS idx_groups_owner ON groups(owner_id);

-- Создание функции для обновления счетчика участников
CREATE OR REPLACE FUNCTION update_group_member_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE groups 
        SET member_count = member_count + 1,
            updated_at = NOW()
        WHERE group_id = NEW.group_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE groups 
        SET member_count = member_count - 1,
            updated_at = NOW()
        WHERE group_id = OLD.group_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Создание триггера для автоматического обновления счетчика
CREATE TRIGGER trigger_update_member_count
    AFTER INSERT OR DELETE ON user_groups
    FOR EACH ROW EXECUTE FUNCTION update_group_member_count();

-- Вставка тестовых данных
INSERT INTO users (user_id, username, display_name) VALUES 
(123456789012345678, 'testuser1', 'Test User 1'),
(234567890123456789, 'testuser2', 'Test User 2'),
(345678901234567890, 'testuser3', 'Test User 3')
ON CONFLICT (user_id) DO NOTHING;

INSERT INTO groups (group_id, channel_id, name, description, owner_id) VALUES
(111111111111111111, 111111111111111111, 'Тестовая группа 1', 'Описание первой группы', 123456789012345678),
(222222222222222222, 222222222222222222, 'Тестовая группа 2', 'Описание второй группы', 234567890123456789),
(333333333333333333, 333333333333333333, 'Группа для заказов', 'Группа для обсуждения заказов', 123456789012345678)
ON CONFLICT (group_id) DO NOTHING;

INSERT INTO user_groups (user_id, group_id, role) VALUES 
(123456789012345678, 111111111111111111, 'owner'),
(234567890123456789, 111111111111111111, 'member'),
(345678901234567890, 111111111111111111, 'member'),
(234567890123456789, 222222222222222222, 'owner'),
(123456789012345678, 222222222222222222, 'member'),
(123456789012345678, 333333333333333333, 'owner'),
(234567890123456789, 333333333333333333, 'admin')
ON CONFLICT (user_id, group_id) DO NOTHING;

-- Создание представления для удобного получения данных
CREATE OR REPLACE VIEW user_groups_view AS
SELECT 
    u.user_id,
    u.username,
    u.display_name,
    g.group_id,
    g.name as group_name,
    g.description as group_description,
    g.member_count,
    ug.joined_at,
    ug.role,
    ug.is_active
FROM users u
JOIN user_groups ug ON u.user_id = ug.user_id
JOIN groups g ON ug.group_id = g.group_id
WHERE ug.is_active = TRUE;

-- Создание таблицы игнорируемых пользователей
CREATE TABLE IF NOT EXISTS ignored_users (
    user_id BIGINT PRIMARY KEY,
    username VARCHAR(100),
    display_name VARCHAR(100),
    added_at TIMESTAMP DEFAULT NOW(),
    reason TEXT
);

-- Индекс для быстрого поиска игнорируемых пользователей
CREATE INDEX IF NOT EXISTS idx_ignored_users_user_id ON ignored_users(user_id);

-- Создание таблицы настроек цветовой схемы
CREATE TABLE IF NOT EXISTS color_settings (
    id SERIAL PRIMARY KEY,
    min_groups INTEGER NOT NULL,
    max_groups INTEGER,
    color VARCHAR(7) NOT NULL,
    description VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Вставка настроек по умолчанию
INSERT INTO color_settings (min_groups, max_groups, color, description) VALUES
(1, 1, '#43b581', 'Зеленый - 1 группа'),
(2, 4, '#faa61a', 'Оранжевый - 2-4 группы'),
(5, 9, '#f04747', 'Красный - 5-9 групп'),
(10, NULL, '#7289da', 'Синий - 10+ групп')
ON CONFLICT DO NOTHING;

-- Функция для поиска групп пользователя
CREATE OR REPLACE FUNCTION get_user_groups(
    p_user_id BIGINT,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0,
    p_search TEXT DEFAULT NULL
)
RETURNS TABLE (
    group_id BIGINT,
    group_name VARCHAR(255),
    group_description TEXT,
    member_count INTEGER,
    joined_at TIMESTAMP,
    role VARCHAR(50)
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        g.group_id,
        g.name,
        g.description,
        g.member_count,
        ug.joined_at,
        ug.role
    FROM user_groups ug
    JOIN groups g ON ug.group_id = g.group_id
    WHERE ug.user_id = p_user_id
        AND ug.is_active = TRUE
        AND (p_search IS NULL OR g.name ILIKE '%' || p_search || '%')
    ORDER BY ug.joined_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;
