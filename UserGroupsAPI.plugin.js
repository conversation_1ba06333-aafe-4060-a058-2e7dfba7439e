/**
 * @name UserGroupsAPI
 * <AUTHOR>
 * @description Показывает информацию о группах пользователей через API (аналог UserGroupsInfo-Popup)
 * @version 1.0.0
 * @source https://github.com/yourusername/discord-groups-api
 * @updateUrl https://github.com/yourusername/discord-groups-api/raw/main/UserGroupsAPI.plugin.js
 */

module.exports = class UserGroupsAPI {
    constructor() {
        // Настройки API
        this.apiUrl = "http://localhost:8000";
        this.requestTimeout = 10000; // 10 секунд

        // Кэш данных
        this.userGroupsCache = new Map();
        this.lastCacheUpdate = 0;
        this.cacheTimeout = 30000; // 30 секунд

        // Кэш игнорируемых пользователей
        this.ignoredUsers = new Set();
        this.lastIgnoredUpdate = 0;
        this.ignoredCacheTimeout = 60000; // 1 минута

        // Кэш настроек цветов
        this.colorSettings = [];
        this.lastColorUpdate = 0;
        this.colorCacheTimeout = 300000; // 5 минут

        // Наблюдатели за DOM
        this.popupObserver = null;
        this.avatarObserver = null;
        this.chatListObserver = null;

        // Кэш информации о чатах
        this.chatMembersCache = new Map();
        this.chatColorCache = new Map();
        this.lastChatUpdate = 0;
        this.chatCacheTimeout = 300000; // 5 минут
    }

    getName() { return "UserGroupsAPI"; }
    getAuthor() { return "YourName"; }
    getDescription() { return "Показывает информацию о группах пользователей через API (аналог UserGroupsInfo-Popup)"; }
    getVersion() { return "1.0.0"; }

    start() {
        console.log("UserGroupsAPI: Плагин запущен");
        BdApi.UI.showToast("UserGroupsAPI запущен", {type: "success"});
        this.startObserving();
        this.startAvatarObserving();
        this.startChatListObserving();
        this.loadIgnoredUsers();
        this.loadColorSettings();
        this.loadGroupColors();

        // Добавляем глобальную функцию для отладки
        window.testChatColors = () => {
            console.log("=== ТЕСТ ЦВЕТОВ ЧАТОВ ===");
            console.log("Размер кэша цветов:", this.chatColorCache.size);
            console.log("Первые 5 записей кэша:", Array.from(this.chatColorCache.entries()).slice(0, 5));
            this.applyChatColors();
        };
    }

    stop() {
        console.log("UserGroupsAPI: Плагин остановлен");
        BdApi.UI.showToast("UserGroupsAPI остановлен", {type: "info"});
        if (this.popupObserver) {
            this.popupObserver.disconnect();
        }
        if (this.avatarObserver) {
            this.avatarObserver.disconnect();
        }
        if (this.chatListObserver) {
            this.chatListObserver.disconnect();
        }
        this.removeAllAvatarBorders();
        this.removeAllChatBorders();
    }

    // Запуск наблюдения за появлением popup'ов
    startObserving() {
        this.popupObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        // Ищем popup профиля пользователя
                        this.checkForUserPopup(node);
                    }
                });
            });
        });

        this.popupObserver.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log("UserGroupsAPI: Наблюдение за popup'ами запущено");
    }

    // Проверка на popup профиля пользователя
    checkForUserPopup(node) {
        // Ищем различные селекторы для popup профиля
        const popupSelectors = [
            '[class*="userPopout"]',
            '[class*="userProfile"]',
            '[class*="popout"]',
            '[data-user-id]'
        ];

        let userPopup = null;
        let userId = null;

        // Проверяем сам элемент
        for (const selector of popupSelectors) {
            if (node.matches && node.matches(selector)) {
                userPopup = node;
                break;
            }
        }

        // Если не найден, ищем внутри
        if (!userPopup) {
            for (const selector of popupSelectors) {
                userPopup = node.querySelector && node.querySelector(selector);
                if (userPopup) break;
            }
        }

        if (userPopup) {
            console.log("UserGroupsAPI: Найден popup пользователя, ищем ID...");
            // Ищем ID пользователя
            userId = this.extractUserIdFromPopup(userPopup);

            if (userId) {
                console.log("UserGroupsAPI: Найден popup пользователя с ID:", userId);
                setTimeout(() => this.addButtonToPopup(userPopup, userId), 100);
            } else {
                console.log("UserGroupsAPI: ID пользователя не найден в popup");
                console.log("UserGroupsAPI: Popup HTML (первые 500 символов):", userPopup.outerHTML.substring(0, 500));
            }
        }
    }

    // Извлечение ID пользователя из popup
    extractUserIdFromPopup(popup) {
        console.log("UserGroupsAPI: Ищем ID пользователя в popup", popup);

        // 1. Ищем в data-user-id на самом popup
        let userId = popup.getAttribute('data-user-id');
        if (userId && /^\d{17,19}$/.test(userId)) {
            console.log("UserGroupsAPI: ID найден в data-user-id popup:", userId);
            return userId;
        }

        // 1.5. Ищем в data-member-id на самом popup (только для неполных профилей с классом user-profile-popout)
        if (popup.classList.contains('user-profile-popout')) {
            userId = popup.getAttribute('data-member-id');
            if (userId && /^\d{17,19}$/.test(userId)) {
                console.log("UserGroupsAPI: ID найден в data-member-id popup:", userId);
                return userId;
            }
        }

        // 2. Ищем в дочерних элементах с data-user-id
        const userIdElements = popup.querySelectorAll('[data-user-id]');
        for (const element of userIdElements) {
            userId = element.getAttribute('data-user-id');
            if (userId && /^\d{17,19}$/.test(userId)) {
                console.log("UserGroupsAPI: ID найден в data-user-id элемента:", userId);
                return userId;
            }
        }

        // 2.5. Ищем в дочерних элементах с data-member-id
        const memberIdElements = popup.querySelectorAll('[data-member-id]');
        for (const element of memberIdElements) {
            userId = element.getAttribute('data-member-id');
            if (userId && /^\d{17,19}$/.test(userId)) {
                console.log("UserGroupsAPI: ID найден в data-member-id элемента:", userId);
                return userId;
            }
        }

        // 3. Ищем в ссылках профиля
        const profileLinks = popup.querySelectorAll('a[href*="/users/"], a[href*="/channels/@me/"]');
        for (const link of profileLinks) {
            const match = link.href.match(/\/users\/(\d{17,19})|\/channels\/@me\/(\d{17,19})/);
            if (match) {
                const foundId = match[1] || match[2];
                console.log("UserGroupsAPI: ID найден в ссылке:", foundId);
                return foundId;
            }
        }

        // 4. Ищем в аватарах
        const avatars = popup.querySelectorAll('img[src*="avatars"], img[src*="cdn.discordapp.com"]');
        for (const avatar of avatars) {
            const match = avatar.src.match(/avatars\/(\d{17,19})\//);
            if (match) {
                console.log("UserGroupsAPI: ID найден в аватаре:", match[1]);
                return match[1];
            }
        }

        // 5. Ищем в классах элементов (иногда ID встроен в классы)
        const elementsWithClasses = popup.querySelectorAll('[class*="user"], [class*="member"], [class*="profile"]');
        for (const element of elementsWithClasses) {
            const classMatch = element.className.match(/(\d{17,19})/);
            if (classMatch) {
                console.log("UserGroupsAPI: ID найден в классе:", classMatch[1]);
                return classMatch[1];
            }
        }

        // 6. Ищем в атрибутах id элементов
        const elementsWithIds = popup.querySelectorAll('[id*="user"], [id*="member"], [id*="profile"]');
        for (const element of elementsWithIds) {
            const idMatch = element.id.match(/(\d{17,19})/);
            if (idMatch) {
                console.log("UserGroupsAPI: ID найден в id атрибуте:", idMatch[1]);
                return idMatch[1];
            }
        }

        // 7. Ищем в любых атрибутах, содержащих числа Discord ID формата
        const allElements = popup.querySelectorAll('*');
        for (const element of allElements) {
            for (const attr of element.attributes) {
                if (attr.value && typeof attr.value === 'string') {
                    const match = attr.value.match(/(\d{17,19})/);
                    if (match && attr.name !== 'style' && attr.name !== 'class') {
                        console.log(`UserGroupsAPI: ID найден в атрибуте ${attr.name}:`, match[1]);
                        return match[1];
                    }
                }
            }
        }

        console.log("UserGroupsAPI: ID пользователя не найден в popup");
        console.log("UserGroupsAPI: HTML popup:", popup.outerHTML.substring(0, 500));
        return null;
    }

    // Добавление кнопки в popup
    addButtonToPopup(popup, userId) {
        // Проверяем, не добавлена ли уже кнопка
        if (popup.querySelector('.groups-api-button')) {
            return;
        }

        const buttonContainer = this.findButtonContainer(popup);
        
        if (buttonContainer) {
            const button = this.createGroupsInfoButton(userId);
            buttonContainer.appendChild(button);
            console.log("UserGroupsAPI: Кнопка добавлена в popup");
        } else {
            // Если не нашли контейнер кнопок, создаем свой
            this.createCustomButtonContainer(popup, userId);
        }
    }

    // Поиск контейнера для кнопок
    findButtonContainer(popup) {
        console.log("UserGroupsAPI: Ищем контейнер для кнопки в popup");

        // Ищем различные возможные контейнеры кнопок
        const selectors = [
            '[class*="button"]',
            '[class*="action"]',
            '[class*="footer"]',
            '[class*="bottom"]',
            'div[style*="flex"]'
        ];

        for (const selector of selectors) {
            const containers = popup.querySelectorAll(selector);
            for (const container of containers) {
                // Проверяем, есть ли уже кнопки в этом контейнере
                if (container.querySelector('button') || container.querySelector('[role="button"]')) {
                    console.log("UserGroupsAPI: Найден контейнер кнопок:", container);
                    return container;
                }
            }
        }



        console.log("UserGroupsAPI: Контейнер для кнопки не найден");
        return null;
    }

    // Создание кнопки
    createGroupsInfoButton(userId) {
        const button = document.createElement('button');
        button.className = 'groups-api-button';
        button.style.cssText = `
            background: #5865f2;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            margin: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: background 0.2s;
        `;

        button.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24">
                <path fill="currentColor" d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 18v-4h2v4h2v2H4c-1.1 0-2-.9-2-2zm0-10V4c0-1.1.9-2 2-2h4v2H4v4H2zm16 10v-4h2v4c0 1.1-.9 2-2 2h-4v-2h4zM12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zm0 8c-1.65 0-3-1.35-3-3s1.35-3 3-3 3 1.35 3 3-1.35 3-3 3z"/>
            </svg>
            Группы (API)
        `;

        button.addEventListener('mouseenter', () => {
            button.style.backgroundColor = '#4752c4';
        });

        button.addEventListener('mouseleave', () => {
            button.style.backgroundColor = '#5865f2';
        });

        button.addEventListener('click', (e) => {
            e.stopPropagation();
            e.preventDefault();
            console.log("UserGroupsAPI: Клик по кнопке для пользователя", userId);
            try {
                this.showUserGroupsInfo(userId);
            } catch (error) {
                console.error("UserGroupsAPI: Ошибка при показе информации:", error);
                BdApi.UI.showToast("Ошибка при загрузке информации", {type: "error"});
            }
        });

        return button;
    }

    // Создание собственного контейнера для кнопки
    createCustomButtonContainer(popup, userId) {
        console.log("UserGroupsAPI: Создаем собственный контейнер для кнопки");

        const container = document.createElement('div');
        container.className = 'groups-api-custom-container';
        container.style.cssText = `
            padding: 12px;
            border-top: 1px solid #2f3136;
            display: flex;
            justify-content: center;
            background: rgba(47, 49, 54, 0.8);
            margin-top: 8px;
        `;

        const button = this.createGroupsInfoButton(userId);
        container.appendChild(button);

        // Пытаемся найти лучшее место для вставки
        const insertTargets = [
            popup.querySelector('[class*="footer"]'),
            popup.querySelector('[class*="bottom"]'),
            popup.querySelector('[class*="actions"]'),
            popup.lastElementChild,
            popup
        ];

        let inserted = false;
        for (const target of insertTargets) {
            if (target && target !== popup) {
                try {
                    target.appendChild(container);
                    inserted = true;
                    console.log("UserGroupsAPI: Контейнер добавлен в:", target);
                    break;
                } catch (e) {
                    console.log("UserGroupsAPI: Не удалось добавить в:", target, e);
                }
            }
        }

        if (!inserted) {
            popup.appendChild(container);
            console.log("UserGroupsAPI: Контейнер добавлен в конец popup");
        }

        console.log("UserGroupsAPI: Создан собственный контейнер для кнопки");
    }

    // API методы
    async apiRequest(endpoint) {
        const url = `${this.apiUrl}${endpoint}`;
        
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

            const response = await fetch(url, {
                signal: controller.signal,
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                mode: 'cors',
                credentials: 'omit'
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error("UserGroupsAPI: API запрос не удался:", error);
            if (error.name === 'AbortError') {
                throw new Error('Превышено время ожидания ответа от сервера');
            }
            if (error.message.includes('CORS') || error.message.includes('Failed to fetch')) {
                throw new Error('Ошибка CORS: убедитесь, что API сервер запущен и настроен правильно');
            }
            throw new Error(`Ошибка API: ${error.message}`);
        }
    }

    async getUserGroups(userId) {
        return await this.apiRequest(`/api/user/${userId}/groups?limit=500`);
    }

    async getGroupUsers(groupId) {
        return await this.apiRequest(`/api/group/${groupId}/users?limit=500`);
    }

    async getColorSettings() {
        return await this.apiRequest('/api/color-settings');
    }

    async getGroupColors() {
        return await this.apiRequest('/api/groups/colors');
    }

    async getGroupColor(groupId) {
        return await this.apiRequest(`/api/group/${groupId}/color`);
    }

    // Навигация к каналу Discord
    navigateToChannel(channelId) {
        try {
            // Получаем модуль навигации Discord
            const NavigationUtils = BdApi.Webpack.getModule(BdApi.Webpack.Filters.byProps("transitionTo"));

            if (NavigationUtils && NavigationUtils.transitionTo) {
                // Переходим к каналу
                NavigationUtils.transitionTo(`/channels/@me/${channelId}`);
                console.log(`UserGroupsAPI: Переход к каналу ${channelId}`);
            } else {
                // Fallback метод
                const ChannelStore = BdApi.Webpack.getModule(BdApi.Webpack.Filters.byProps("getChannel"));
                const PrivateChannelActions = BdApi.Webpack.getModule(BdApi.Webpack.Filters.byProps("selectPrivateChannel"));

                if (PrivateChannelActions && PrivateChannelActions.selectPrivateChannel) {
                    PrivateChannelActions.selectPrivateChannel(channelId);
                    console.log(`UserGroupsAPI: Fallback переход к каналу ${channelId}`);
                } else {
                    throw new Error("Не удалось найти модули навигации Discord");
                }
            }
        } catch (error) {
            console.error("UserGroupsAPI: Ошибка навигации:", error);
            BdApi.UI.showToast(`Не удалось перейти к каналу: ${error.message}`, {type: "error"});
        }
    }

    // Показ информации о группах пользователя
    async showUserGroupsInfo(userId) {
        console.log("UserGroupsAPI: Показываем информацию для пользователя", userId);

        try {
            // Получаем данные пользователя из Discord
            const UserStore = BdApi.Webpack.getModule(BdApi.Webpack.Filters.byProps("getUser"));
            const user = UserStore?.getUser(userId);

            if (!user) {
                console.error("UserGroupsAPI: Пользователь не найден", userId);
                BdApi.UI.showToast("Пользователь не найден", {type: "error"});
                return;
            }

            console.log("UserGroupsAPI: Пользователь найден:", user);

            // Получаем группы пользователя через API
            const data = await this.getUserGroups(userId);
            const userGroups = data.groups || [];

            console.log("UserGroupsAPI: Найдено групп:", userGroups.length);

            // Если пользователь не найден в базе, показываем соответствующее сообщение
            if (userGroups.length === 0 && data.total === 0) {
                const modalContent = `
                    <div style="padding: 20px; text-align: center;">
                        <div style="display: flex; align-items: center; margin-bottom: 20px;">
                            <img src="${user.avatar ? `https://cdn.discordapp.com/avatars/${user.id}/${user.avatar}.png?size=64` : 'https://cdn.discordapp.com/embed/avatars/0.png'}"
                                 style="width: 48px; height: 48px; border-radius: 50%; margin-right: 15px;" />
                            <div>
                                <h3 style="margin: 0; color: #fff;">${displayName}</h3>
                                <p style="margin: 0; color: #b9bbbe; font-size: 14px;">${user.username}#${user.discriminator || '0000'}</p>
                            </div>
                        </div>

                        <div style="background: #2f3136; padding: 20px; border-radius: 8px;">
                            <h4 style="margin: 0 0 15px 0; color: #f04747;">❌ Пользователь не найден</h4>
                            <p style="margin: 0; color: #dcddde; line-height: 1.5;">
                                Этот пользователь не найден в базе данных групп.<br>
                                Возможно, он не участвует в групповых чатах или<br>
                                данные еще не были импортированы.
                            </p>
                        </div>
                    </div>
                `;

                this.showCustomModal(modalContent, `Группы пользователя ${displayName}`);
                return;
            }

            const displayName = user.globalName || user.username;

            const modalContent = `
                <div style="padding: 20px; max-height: 500px; overflow-y: auto;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <img src="${user.avatar ? `https://cdn.discordapp.com/avatars/${user.id}/${user.avatar}.png?size=64` : 'https://cdn.discordapp.com/embed/avatars/0.png'}"
                             style="width: 48px; height: 48px; border-radius: 50%; margin-right: 15px;" />
                        <div>
                            <h3 style="margin: 0; color: #fff;">${displayName}</h3>
                            <p style="margin: 0; color: #b9bbbe; font-size: 14px;">${user.username}#${user.discriminator || '0000'}</p>
                        </div>
                    </div>

                    <div style="background: #2f3136; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #5865f2;">📊 Статистика</h4>
                        <p style="margin: 5px 0; color: #dcddde;">Показано групп: <strong>${userGroups.length}</strong></p>
                        ${data.total ? `<p style="margin: 5px 0; color: #dcddde;">Всего групп: <strong>${data.total}</strong></p>` : ''}
                        ${data.hasMore ? `<p style="margin: 5px 0; color: #f04747; font-size: 12px;">⚠️ Показаны только первые ${userGroups.length} групп</p>` : ''}
                    </div>

                    ${userGroups.length > 0 ? `
                        <div>
                            <h4 style="margin: 0 0 15px 0; color: #5865f2;">💬 Группы</h4>
                            <div style="background: #2f3136; padding: 10px; border-radius: 6px; margin-bottom: 15px; font-size: 12px; color: #b9bbbe;">
                                💡 <strong>Левый клик</strong> - перейти в группу | <strong>Правый клик</strong> - показать участников
                            </div>
                            <div style="max-height: 300px; overflow-y: auto;">
                                ${userGroups.map(group => `
                                    <div class="group-item" data-group-id="${group.group_id}" style="
                                        background: #40444b;
                                        padding: 12px;
                                        margin-bottom: 8px;
                                        border-radius: 6px;
                                        cursor: pointer;
                                        transition: background 0.2s;
                                    ">
                                        <div style="font-weight: 500; color: #fff; margin-bottom: 4px;">${group.group_name || group.name || 'Без названия'}</div>
                                        <div style="font-size: 12px; color: #b9bbbe;">
                                            👥 ${group.member_count} участников
                                            ${group.role === 'owner' ? ' | 👑 Владелец' : ' | 👤 Участник'}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : `
                        <div style="text-align: center; color: #b9bbbe; padding: 20px;">
                            <p>Пользователь не состоит в групповых чатах</p>
                        </div>
                    `}
                </div>
            `;

            this.showCustomModal(modalContent, `Группы пользователя ${displayName}`);
        } catch (error) {
            console.error("UserGroupsAPI: Ошибка при получении данных:", error);
            BdApi.UI.showToast(`Ошибка: ${error.message}`, {type: "error"});
        }
    }

    // Создание собственного модального окна
    showCustomModal(content, title) {
        // Создаем overlay
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.85);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // Создаем модальное окно
        const modal = document.createElement('div');
        modal.style.cssText = `
            background: #36393f;
            border-radius: 8px;
            max-width: 600px;
            max-height: 80vh;
            width: 90%;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.24);
            overflow: hidden;
        `;

        // Заголовок
        const header = document.createElement('div');
        header.style.cssText = `
            background: #2f3136;
            padding: 20px;
            border-bottom: 1px solid #202225;
            display: flex;
            justify-content: space-between;
            align-items: center;
        `;

        const titleElement = document.createElement('h2');
        titleElement.style.cssText = `
            margin: 0;
            color: #fff;
            font-size: 20px;
            font-weight: 600;
        `;
        titleElement.textContent = title;

        const closeButton = document.createElement('button');
        closeButton.style.cssText = `
            background: none;
            border: none;
            color: #b9bbbe;
            font-size: 24px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
        `;
        closeButton.innerHTML = '×';
        closeButton.addEventListener('click', () => {
            document.body.removeChild(overlay);
        });

        header.appendChild(titleElement);
        header.appendChild(closeButton);

        // Контент
        const contentDiv = document.createElement('div');
        contentDiv.innerHTML = content;

        modal.appendChild(header);
        modal.appendChild(contentDiv);
        overlay.appendChild(modal);

        // Закрытие по клику на overlay
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                document.body.removeChild(overlay);
            }
        });

        // Добавляем в DOM
        document.body.appendChild(overlay);

        // Добавляем обработчики для групп
        setTimeout(() => {
            const groupItems = modal.querySelectorAll('.group-item');
            groupItems.forEach(item => {
                item.addEventListener('mouseenter', () => {
                    item.style.backgroundColor = '#5865f2';
                });
                item.addEventListener('mouseleave', () => {
                    item.style.backgroundColor = '#40444b';
                });
                item.addEventListener('click', () => {
                    const groupId = item.getAttribute('data-group-id');
                    // Переходим к каналу Discord
                    this.navigateToChannel(groupId);
                    // Закрываем модальное окно
                    document.body.removeChild(overlay);
                });

                // Правый клик для просмотра участников
                item.addEventListener('contextmenu', async (e) => {
                    e.preventDefault();
                    const groupId = item.getAttribute('data-group-id');
                    await this.showGroupUsers(groupId);
                });
            });
        }, 100);
    }

    // Показ участников группы
    async showGroupUsers(groupId) {
        try {
            console.log("UserGroupsAPI: Показываем участников группы", groupId);

            const data = await this.getGroupUsers(groupId);
            const users = data.users || [];

            // Находим название группы
            const groupName = users.length > 0 ? `Группа ${groupId}` : 'Неизвестная группа';

            const modalContent = `
                <div style="padding: 20px; max-height: 500px; overflow-y: auto;">
                    <div style="background: #2f3136; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #5865f2;">📊 Статистика</h4>
                        <p style="margin: 5px 0; color: #dcddde;">Участников: <strong>${users.length}</strong></p>
                    </div>

                    ${users.length > 0 ? `
                        <div>
                            <h4 style="margin: 0 0 15px 0; color: #5865f2;">👥 Участники</h4>
                            <div style="max-height: 400px; overflow-y: auto;">
                                ${users.map(user => `
                                    <div style="
                                        background: #40444b;
                                        padding: 12px;
                                        margin-bottom: 8px;
                                        border-radius: 6px;
                                        display: flex;
                                        align-items: center;
                                    ">
                                        ${user.avatar_url ? `<img src="${user.avatar_url}" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 10px;" />` : ''}
                                        <div>
                                            <div style="font-weight: 500; color: #fff; margin-bottom: 2px;">${user.display_name || user.username}</div>
                                            <div style="font-size: 12px; color: #b9bbbe;">
                                                @${user.username} | ${user.role === 'owner' ? '👑 Владелец' : '👤 Участник'}
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : `
                        <div style="text-align: center; color: #b9bbbe; padding: 20px;">
                            <p>Участники не найдены</p>
                        </div>
                    `}
                </div>
            `;

            this.showCustomModal(modalContent, `Участники группы`);
        } catch (error) {
            console.error("UserGroupsAPI: Ошибка при получении участников группы:", error);
            BdApi.UI.showToast(`Ошибка: ${error.message}`, {type: "error"});
        }
    }

    // === НОВАЯ ФУНКЦИОНАЛЬНОСТЬ: ЦВЕТОВАЯ ОКАНТОВКА АВАТАРОВ ===

    // Загрузка списка игнорируемых пользователей
    async loadIgnoredUsers() {
        try {
            const now = Date.now();
            if (now - this.lastIgnoredUpdate < this.ignoredCacheTimeout) {
                return; // Используем кэш
            }

            const data = await this.apiRequest('/api/ignored-users/list');
            if (data.success && Array.isArray(data.ignoredUsers)) {
                this.ignoredUsers = new Set(data.ignoredUsers);
                this.lastIgnoredUpdate = now;
                console.log("UserGroupsAPI: Загружен список игнорируемых пользователей:", this.ignoredUsers.size);
            }
        } catch (error) {
            console.error("UserGroupsAPI: Ошибка загрузки игнорируемых пользователей:", error);
        }
    }

    // Загрузка настроек цветов
    async loadColorSettings() {
        try {
            const now = Date.now();
            if (now - this.lastColorUpdate < this.colorCacheTimeout) {
                return; // Используем кэш
            }

            const data = await this.getColorSettings();
            if (data.success && Array.isArray(data.colorSettings)) {
                this.colorSettings = data.colorSettings.sort((a, b) => a.min_groups - b.min_groups);
                this.lastColorUpdate = now;
                console.log("UserGroupsAPI: Загружены настройки цветов:", this.colorSettings.length);
            }
        } catch (error) {
            console.error("UserGroupsAPI: Ошибка загрузки настроек цветов:", error);
            // Используем настройки по умолчанию
            this.colorSettings = [
                { min_groups: 1, max_groups: 1, color: '#43b581', description: 'Зеленый - 1 группа' },
                { min_groups: 2, max_groups: 4, color: '#faa61a', description: 'Оранжевый - 2-4 группы' },
                { min_groups: 5, max_groups: 9, color: '#f04747', description: 'Красный - 5-9 групп' },
                { min_groups: 10, max_groups: null, color: '#7289da', description: 'Синий - 10+ групп' }
            ];
        }
    }

    // Запуск наблюдения за аватарами в групповых чатах
    startAvatarObserving() {
        this.avatarObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        this.processAvatarsInNode(node);
                    }
                });
            });
        });

        this.avatarObserver.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Обрабатываем уже существующие аватары
        this.processAvatarsInNode(document.body);

        console.log("UserGroupsAPI: Наблюдение за аватарами запущено");
    }

    // Обработка аватаров в узле DOM
    processAvatarsInNode(node) {
        // Ищем аватары в сообщениях групповых чатов
        const avatarSelectors = [
            'img[src*="avatars"]', // Основные аватары
            '[class*="avatar"] img', // Аватары в контейнерах
            '[class*="message"] img[src*="avatars"]' // Аватары в сообщениях
        ];

        for (const selector of avatarSelectors) {
            const avatars = node.querySelectorAll ? node.querySelectorAll(selector) : [];

            // Если сам node является аватаром
            if (node.matches && node.matches(selector)) {
                this.processAvatar(node);
            }

            // Обрабатываем найденные аватары
            avatars.forEach(avatar => this.processAvatar(avatar));
        }
    }

    // Обработка отдельного аватара
    async processAvatar(avatarImg) {
        try {
            // Проверяем, что это групповой чат (не DM)
            if (!this.isInGroupChat()) {
                return;
            }

            // Извлекаем ID пользователя из URL аватара
            const userId = this.extractUserIdFromAvatar(avatarImg);
            if (!userId) {
                return;
            }

            // Проверяем, не игнорируется ли пользователь
            if (this.ignoredUsers.has(userId)) {
                return; // Не показываем окантовку для игнорируемых
            }

            // Проверяем, не обработан ли уже этот аватар
            if (avatarImg.dataset.groupsApiProcessed === userId) {
                return;
            }

            // Получаем количество групп пользователя
            const groupCount = await this.getUserGroupCount(userId);

            // Добавляем цветовую окантовку
            this.addAvatarBorder(avatarImg, groupCount, userId);

        } catch (error) {
            console.error("UserGroupsAPI: Ошибка обработки аватара:", error);
        }
    }

    // Проверка, находимся ли мы в групповом чате
    isInGroupChat() {
        // Проверяем URL - групповые чаты имеют формат /channels/@me/CHANNEL_ID
        const url = window.location.href;
        const isGroupChat = url.includes('/channels/@me/') && !url.includes('/channels/@me/friends');

        // Дополнительная проверка через заголовок чата
        const chatHeader = document.querySelector('[class*="title"]');
        const hasMultipleMembers = chatHeader && chatHeader.textContent &&
                                 (chatHeader.textContent.includes('участник') ||
                                  chatHeader.textContent.includes('member'));

        return isGroupChat || hasMultipleMembers;
    }

    // Извлечение ID пользователя из URL аватара
    extractUserIdFromAvatar(avatarImg) {
        if (!avatarImg.src) return null;

        const match = avatarImg.src.match(/avatars\/(\d{17,19})\//);
        return match ? match[1] : null;
    }

    // Получение количества групп пользователя (с кэшированием)
    async getUserGroupCount(userId) {
        try {
            // Проверяем кэш
            const cached = this.userGroupsCache.get(userId);
            const now = Date.now();

            if (cached && (now - cached.timestamp < this.cacheTimeout)) {
                return cached.count;
            }

            // Загружаем данные с сервера
            const data = await this.getUserGroups(userId);
            const count = data.total || 0;

            // Сохраняем в кэш
            this.userGroupsCache.set(userId, {
                count: count,
                timestamp: now
            });

            return count;
        } catch (error) {
            console.error("UserGroupsAPI: Ошибка получения количества групп:", error);
            return 0;
        }
    }

    // Определение цвета на основе количества групп и настроек
    getColorForGroupCount(groupCount) {
        if (groupCount === 0) {
            return null; // Не показываем окантовку для 0 групп
        }

        // Ищем подходящую настройку цвета
        for (const setting of this.colorSettings) {
            const minGroups = setting.min_groups;
            const maxGroups = setting.max_groups;

            if (maxGroups === null) {
                // Если max_groups = null, значит это настройка для "X+ групп"
                if (groupCount >= minGroups) {
                    return setting.color;
                }
            } else {
                // Проверяем диапазон
                if (groupCount >= minGroups && groupCount <= maxGroups) {
                    return setting.color;
                }
            }
        }

        // Если не найдена подходящая настройка, используем цвет по умолчанию
        return '#43b581';
    }

    // Добавление цветовой окантовки к аватару
    addAvatarBorder(avatarImg, groupCount, userId) {
        // Определяем цвет на основе настроек
        const borderColor = this.getColorForGroupCount(groupCount);

        if (!borderColor) {
            return; // Не показываем окантовку
        }

        // Применяем стили
        avatarImg.style.border = `3px solid ${borderColor}`;
        avatarImg.style.borderRadius = '50%';
        avatarImg.style.boxSizing = 'border-box';

        // Добавляем тень для лучшей видимости
        avatarImg.style.boxShadow = `0 0 8px ${borderColor}40`;

        // Помечаем как обработанный
        avatarImg.dataset.groupsApiProcessed = userId;
        avatarImg.dataset.groupsApiCount = groupCount;

        console.log(`UserGroupsAPI: Добавлена окантовка для пользователя ${userId} (${groupCount} групп, цвет: ${borderColor})`);
    }

    // Удаление всех окантовок аватаров
    removeAllAvatarBorders() {
        const processedAvatars = document.querySelectorAll('[data-groups-api-processed]');
        processedAvatars.forEach(avatar => {
            avatar.style.border = '';
            avatar.style.boxShadow = '';
            delete avatar.dataset.groupsApiProcessed;
            delete avatar.dataset.groupsApiCount;
        });
        console.log("UserGroupsAPI: Удалены все окантовки аватаров");
    }

    // === ФУНКЦИОНАЛЬНОСТЬ ЦВЕТОВ ЧАТОВ ===

    // Загрузка карты цветов групп
    async loadGroupColors() {
        try {
            console.log("UserGroupsAPI: Начинаем загрузку карты цветов групп...");
            const data = await this.apiRequest('/api/groups/colors');
            console.log("UserGroupsAPI: Ответ сервера на /api/groups/colors:", data);

            if (data.success && Array.isArray(data.colors)) {
                // Создаем Map для быстрого поиска
                this.chatColorCache.clear();
                data.colors.forEach(colorData => {
                    this.chatColorCache.set(colorData.group_id.toString(), {
                        color: colorData.color,
                        dominantUserId: colorData.dominant_user_id,
                        dominantGroupCount: colorData.dominant_group_count,
                        updatedAt: colorData.updated_at
                    });
                });

                console.log("UserGroupsAPI: Загружена карта цветов групп:", this.chatColorCache.size);
                console.log("UserGroupsAPI: Первые 5 записей карты:", Array.from(this.chatColorCache.entries()).slice(0, 5));

                // Применяем цвета к уже видимым чатам
                setTimeout(() => {
                    this.applyChatColors();
                }, 2000); // Даем время DOM загрузиться
            } else {
                console.warn("UserGroupsAPI: Неверный формат ответа или пустая карта цветов");
            }
        } catch (error) {
            console.error("UserGroupsAPI: Ошибка загрузки карты цветов групп:", error);
        }
    }

    // Запуск наблюдения за списком чатов
    startChatListObserving() {
        this.chatListObserver = new MutationObserver((mutations) => {
            let shouldApplyColors = false;

            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        // Проверяем, добавились ли элементы чатов
                        if (this.isChatListElement(node)) {
                            shouldApplyColors = true;
                        }
                    }
                });
            });

            if (shouldApplyColors) {
                // Debounce для избежания частых вызовов при скролле
                clearTimeout(this.chatColorTimeout);
                this.chatColorTimeout = setTimeout(() => {
                    this.applyChatColors();
                }, 100);
            }
        });

        this.chatListObserver.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log("UserGroupsAPI: Наблюдение за списком чатов запущено");
    }

    // Проверка, является ли элемент частью списка чатов
    isChatListElement(node) {
        if (!node.querySelector) return false;

        // Ищем элементы, которые могут быть чатами в списке
        const chatSelectors = [
            '[class*="channel"]',
            '[class*="private"]',
            '[class*="dm"]',
            '[data-list-item-id*="@me"]'
        ];

        for (const selector of chatSelectors) {
            if (node.matches && node.matches(selector)) return true;
            if (node.querySelector(selector)) return true;
        }

        return false;
    }

    // Применение цветов к чатам
    applyChatColors() {
        try {
            console.log("UserGroupsAPI: Начинаем применение цветов к чатам...");

            // Ищем все элементы чатов в списке слева
            const chatElements = this.findChatElements();
            console.log("UserGroupsAPI: Найдено элементов чатов:", chatElements.length);

            let processedCount = 0;
            chatElements.forEach((chatElement, index) => {
                const channelId = this.extractChannelIdFromChatElement(chatElement);
                console.log(`UserGroupsAPI: Чат ${index}: channelId=${channelId}`);

                if (channelId) {
                    const isGroupDM = this.isGroupDMChannel(channelId);
                    console.log(`UserGroupsAPI: Канал ${channelId} - групповой DM: ${isGroupDM}`);

                    if (isGroupDM) {
                        this.applyChatColor(chatElement, channelId);
                        processedCount++;
                    }
                }
            });

            console.log(`UserGroupsAPI: Обработано групповых чатов: ${processedCount}`);

        } catch (error) {
            console.error("UserGroupsAPI: Ошибка применения цветов чатов:", error);
        }
    }

    // Поиск элементов чатов в DOM
    findChatElements() {
        console.log("UserGroupsAPI: Ищем элементы чатов...");

        const selectors = [
            '[data-list-item-id*="@me"]', // Основной селектор для DM
            'a[href*="/channels/@me/"]', // Ссылки на DM каналы
            '[class*="channel"][class*="private"]', // Альтернативный селектор
            '[class*="dm"]' // Еще один вариант
        ];

        const elements = [];
        selectors.forEach((selector, index) => {
            console.log(`UserGroupsAPI: Пробуем селектор ${index}: ${selector}`);
            const found = document.querySelectorAll(selector);
            console.log(`UserGroupsAPI: Найдено по селектору ${index}: ${found.length} элементов`);

            found.forEach(el => {
                if (!elements.includes(el)) {
                    elements.push(el);
                }
            });
        });

        console.log(`UserGroupsAPI: Всего уникальных элементов чатов: ${elements.length}`);
        return elements;
    }

    // Извлечение ID канала из элемента чата
    extractChannelIdFromChatElement(chatElement) {
        // Пробуем разные способы получения ID канала
        const dataId = chatElement.getAttribute('data-list-item-id');
        if (dataId && dataId.includes('@me/')) {
            return dataId.split('@me/')[1];
        }

        // Альтернативные способы
        const href = chatElement.querySelector('a')?.href;
        if (href && href.includes('/channels/@me/')) {
            const match = href.match(/\/channels\/@me\/(\d+)/);
            return match ? match[1] : null;
        }

        return null;
    }

    // Проверка, является ли канал групповым DM
    isGroupDMChannel(channelId) {
        try {
            const ChannelStore = BdApi.Webpack.getModule(BdApi.Webpack.Filters.byProps("getChannel"));
            console.log("UserGroupsAPI: ChannelStore найден:", !!ChannelStore);

            if (!ChannelStore) {
                console.warn("UserGroupsAPI: ChannelStore не найден, пробуем альтернативный способ");
                // Альтернативный способ - проверяем URL
                return this.isGroupDMByUrl(channelId);
            }

            const channel = ChannelStore.getChannel(channelId);
            console.log(`UserGroupsAPI: Канал ${channelId}:`, channel);

            if (!channel) {
                console.warn(`UserGroupsAPI: Канал ${channelId} не найден в ChannelStore`);
                return this.isGroupDMByUrl(channelId);
            }

            const isGroupDM = channel.type === 3; // GROUP_DM = 3
            console.log(`UserGroupsAPI: Канал ${channelId} тип ${channel.type}, групповой DM: ${isGroupDM}`);
            return isGroupDM;
        } catch (error) {
            console.error("UserGroupsAPI: Ошибка проверки типа канала:", error);
            return this.isGroupDMByUrl(channelId);
        }
    }

    // Альтернативный способ проверки через URL и DOM
    isGroupDMByUrl(channelId) {
        try {
            // Проверяем текущий URL
            const currentUrl = window.location.href;
            if (currentUrl.includes(`/channels/@me/${channelId}`)) {
                // Если мы сейчас в этом канале, проверяем есть ли несколько участников
                const membersList = document.querySelector('[class*="members"]');
                if (membersList) {
                    const members = membersList.querySelectorAll('[class*="member"]');
                    const isGroup = members.length > 1; // Больше 1 участника = группа
                    console.log(`UserGroupsAPI: Альтернативная проверка канала ${channelId}: ${members.length} участников, группа: ${isGroup}`);
                    return isGroup;
                }
            }

            // Проверяем по элементу в списке чатов
            const chatElement = document.querySelector(`[data-list-item-id*="${channelId}"]`);
            if (chatElement) {
                // Ищем индикаторы группового чата
                const hasGroupIcon = chatElement.querySelector('[class*="group"]') ||
                                   chatElement.querySelector('svg[class*="group"]') ||
                                   chatElement.textContent.includes('участник');

                console.log(`UserGroupsAPI: Проверка по DOM элементу канала ${channelId}: групповой = ${!!hasGroupIcon}`);
                return !!hasGroupIcon;
            }

            console.log(`UserGroupsAPI: Не удалось определить тип канала ${channelId}, считаем групповым`);
            return true; // По умолчанию считаем групповым, чтобы попробовать применить цвет
        } catch (error) {
            console.error("UserGroupsAPI: Ошибка альтернативной проверки канала:", error);
            return true; // По умолчанию считаем групповым
        }
    }

    // Применение цвета к конкретному чату
    async applyChatColor(chatElement, channelId) {
        try {
            // Проверяем, не обработан ли уже этот чат
            if (chatElement.dataset.groupsApiChatProcessed === channelId) {
                return;
            }

            let colorData = this.chatColorCache.get(channelId);

            if (!colorData) {
                // On-demand запрос с debounce
                colorData = await this.requestChatColorWithDebounce(channelId);
            }

            if (colorData && colorData.color) {
                this.applyChatBorder(chatElement, colorData.color, channelId);
            }

        } catch (error) {
            console.error("UserGroupsAPI: Ошибка применения цвета чата:", error);
        }
    }

    // On-demand запрос цвета с debounce (15 секунд)
    async requestChatColorWithDebounce(channelId) {
        // Отменяем предыдущий таймер для этого канала
        if (this.chatColorRequests && this.chatColorRequests[channelId]) {
            clearTimeout(this.chatColorRequests[channelId]);
        }

        if (!this.chatColorRequests) {
            this.chatColorRequests = {};
        }

        return new Promise((resolve) => {
            this.chatColorRequests[channelId] = setTimeout(async () => {
                try {
                    const data = await this.apiRequest(`/api/group/${channelId}/color`);
                    if (data.success) {
                        const colorData = {
                            color: data.color,
                            dominantUserId: data.dominant_user_id,
                            dominantGroupCount: data.dominant_group_count,
                            updatedAt: data.updated_at
                        };

                        // Сохраняем в кэш
                        this.chatColorCache.set(channelId, colorData);

                        console.log(`UserGroupsAPI: Получен цвет для чата ${channelId}: ${data.color}`);
                        resolve(colorData);
                    } else {
                        resolve(null);
                    }
                } catch (error) {
                    console.error(`UserGroupsAPI: Ошибка получения цвета чата ${channelId}:`, error);
                    resolve(null);
                }

                delete this.chatColorRequests[channelId];
            }, 15000); // 15 секунд задержка
        });
    }

    // Применение окантовки к чату
    applyChatBorder(chatElement, color, channelId) {
        console.log(`UserGroupsAPI: Применяем окантовку к чату ${channelId} цветом ${color}`);

        // Ищем аватар чата по разным селекторам
        const avatarSelectors = [
            'img[class*="avatar"]',
            '.avatar__44b0c img',
            '[class*="avatar"] img',
            'img[src*="channel-icons"]',
            'img[src*="avatars"]',
            'svg foreignObject img'
        ];

        let avatarElement = null;
        for (const selector of avatarSelectors) {
            avatarElement = chatElement.querySelector(selector);
            if (avatarElement) {
                console.log(`UserGroupsAPI: Найден аватар по селектору: ${selector}`);
                break;
            }
        }

        if (avatarElement) {
            // Применяем стили к аватару
            avatarElement.style.border = `3px solid ${color}`;
            avatarElement.style.borderRadius = '50%';
            avatarElement.style.boxSizing = 'border-box';
            avatarElement.style.boxShadow = `0 0 8px ${color}80`;
            console.log(`UserGroupsAPI: Стили применены к аватару`);
        } else {
            console.log(`UserGroupsAPI: Аватар не найден, применяем к элементу чата`);
            // Если нет аватара, применяем к самому элементу чата
            chatElement.style.borderLeft = `4px solid ${color}`;
            chatElement.style.boxShadow = `inset 4px 0 0 ${color}40`;
        }

        // Также попробуем найти wrapper аватара для дополнительной окантовки
        const wrapperElement = chatElement.querySelector('[class*="wrapper"]');
        if (wrapperElement) {
            wrapperElement.style.border = `2px solid ${color}`;
            wrapperElement.style.borderRadius = '50%';
            wrapperElement.style.boxSizing = 'border-box';
            console.log(`UserGroupsAPI: Дополнительная окантовка применена к wrapper`);
        }

        // Помечаем как обработанный
        chatElement.dataset.groupsApiChatProcessed = channelId;

        console.log(`UserGroupsAPI: Окантовка чата ${channelId} применена успешно`);
    }

    // Удаление всех окантовок чатов
    removeAllChatBorders() {
        const processedChats = document.querySelectorAll('[data-groups-api-chat-processed]');
        processedChats.forEach(chatElement => {
            // Убираем стили с аватара
            const avatarElement = chatElement.querySelector('img, [class*="avatar"]');
            if (avatarElement) {
                avatarElement.style.border = '';
                avatarElement.style.boxShadow = '';
            }

            // Убираем стили с самого элемента
            chatElement.style.borderLeft = '';
            chatElement.style.boxShadow = '';

            delete chatElement.dataset.groupsApiChatProcessed;
        });

        console.log("UserGroupsAPI: Удалены все окантовки чатов");
    }
};
