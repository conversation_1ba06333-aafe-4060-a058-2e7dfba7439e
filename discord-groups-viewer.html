<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discord Groups Analyzer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #36393f;
            color: #dcddde;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #2f3136;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }

        .header h1 {
            color: #5865f2;
            margin-bottom: 10px;
        }

        .file-input {
            margin: 20px 0;
            text-align: center;
        }

        .file-input input[type="file"] {
            display: none;
        }

        .file-input label {
            background: #5865f2;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            display: inline-block;
            transition: background 0.3s;
        }

        .file-input label:hover {
            background: #4752c4;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #2f3136;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-card h3 {
            color: #5865f2;
            margin-bottom: 10px;
        }

        .stat-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #00d166;
        }

        .tabs {
            display: flex;
            background: #2f3136;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 15px;
            background: #40444b;
            color: #b9bbbe;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
        }

        .tab.active {
            background: #5865f2;
            color: white;
        }

        .tab-content {
            background: #2f3136;
            padding: 20px;
            border-radius: 0 0 8px 8px;
            min-height: 400px;
        }

        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
        }

        .user-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 5px;
            background: #40444b;
            border-radius: 6px;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin-right: 10px;
            background: #5865f2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .user-name {
            font-weight: 500;
        }

        .user-count {
            background: #5865f2;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.9em;
        }

        .group-item {
            background: #40444b;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 6px;
        }

        .group-name {
            font-weight: bold;
            color: #00d166;
            margin-bottom: 5px;
        }

        .group-meta {
            font-size: 0.9em;
            color: #b9bbbe;
            margin-bottom: 10px;
        }

        .participants {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .participant {
            background: #5865f2;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.8em;
        }

        .search-box {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            background: #40444b;
            border: 1px solid #72767d;
            border-radius: 6px;
            color: #dcddde;
            font-size: 16px;
        }

        .search-box:focus {
            outline: none;
            border-color: #5865f2;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #b9bbbe;
        }

        .error {
            background: #ed4245;
            color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Discord Groups Analyzer</h1>
            <p>Анализ групповых чатов Discord</p>
        </div>

        <div class="file-input">
            <label for="jsonFile">
                📁 Загрузить JSON файл
                <input type="file" id="jsonFile" accept=".json" />
            </label>
        </div>

        <div id="content" style="display: none;">
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Всего групп</h3>
                    <div class="number" id="totalGroups">0</div>
                </div>
                <div class="stat-card">
                    <h3>Уникальных пользователей</h3>
                    <div class="number" id="totalUsers">0</div>
                </div>
                <div class="stat-card">
                    <h3>Средний размер группы</h3>
                    <div class="number" id="avgGroupSize">0</div>
                </div>
                <div class="stat-card">
                    <h3>Дата экспорта</h3>
                    <div class="number" id="exportDate" style="font-size: 1em;">-</div>
                </div>
            </div>

            <div class="tabs">
                <div class="tab active" onclick="switchTab('users')">👥 По пользователям</div>
                <div class="tab" onclick="switchTab('groups')">💬 Все группы</div>
                <div class="tab" onclick="switchTab('owners')">👑 Владельцы групп</div>
            </div>

            <div class="tab-content">
                <div id="users-panel" class="tab-panel active">
                    <input type="text" class="search-box" id="userSearch" placeholder="🔍 Поиск пользователей..." />
                    <div id="usersList" class="user-list"></div>
                </div>

                <div id="groups-panel" class="tab-panel">
                    <input type="text" class="search-box" id="groupSearch" placeholder="🔍 Поиск групп..." />
                    <div id="groupsList"></div>
                </div>

                <div id="owners-panel" class="tab-panel">
                    <input type="text" class="search-box" id="ownerSearch" placeholder="🔍 Поиск владельцев..." />
                    <div id="ownersList" class="user-list"></div>
                </div>
            </div>
        </div>

        <div id="loading" class="loading" style="display: none;">
            ⏳ Обработка данных...
        </div>

        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        let data = null;
        let userStats = {};
        let ownerStats = {};

        document.getElementById('jsonFile').addEventListener('change', handleFileSelect);
        document.getElementById('userSearch').addEventListener('input', filterUsers);
        document.getElementById('groupSearch').addEventListener('input', filterGroups);
        document.getElementById('ownerSearch').addEventListener('input', filterOwners);

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            document.getElementById('loading').style.display = 'block';
            document.getElementById('error').style.display = 'none';

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    data = JSON.parse(e.target.result);
                    processData();
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('content').style.display = 'block';
                } catch (error) {
                    showError('Ошибка при чтении файла: ' + error.message);
                    document.getElementById('loading').style.display = 'none';
                }
            };
            reader.readAsText(file);
        }

        function processData() {
            userStats = {};
            ownerStats = {};

            // Подсчет статистики по пользователям
            data.groups.forEach(group => {
                // Подсчет владельцев
                if (group.ownerId) {
                    const owner = group.recipients.find(r => r.id === group.ownerId) || group.owner;
                    if (owner) {
                        const key = owner.id; // Изменено: используем ID вместо имени
                        if (!ownerStats[key]) {
                            ownerStats[key] = {
                                count: 0,
                                user: owner
                            };
                        }
                        ownerStats[key].count++;
                    }
                }

                // Подсчет участников
                group.recipients.forEach(recipient => {
                    const key = recipient.id; // Изменено: используем ID вместо имени
                    if (!userStats[key]) {
                        userStats[key] = {
                            count: 0,
                            user: recipient,
                            groups: []
                        };
                    }
                    userStats[key].count++;
                    userStats[key].groups.push(group);
                });
            });

            updateStats();
            renderUsers();
            renderGroups();
            renderOwners();
        }

        function updateStats() {
            document.getElementById('totalGroups').textContent = data.groups.length.toLocaleString();
            document.getElementById('totalUsers').textContent = Object.keys(userStats).length.toLocaleString();

            const avgSize = data.groups.reduce((sum, group) => sum + group.participantCount, 0) / data.groups.length;
            document.getElementById('avgGroupSize').textContent = avgSize.toFixed(1);

            const exportDate = new Date(data.metadata.exportedAt).toLocaleDateString('ru-RU');
            document.getElementById('exportDate').textContent = exportDate;
        }

        function renderUsers() {
            const usersList = document.getElementById('usersList');
            const sortedUsers = Object.entries(userStats)
                .sort((a, b) => b[1].count - a[1].count);

            usersList.innerHTML = sortedUsers.map(([userId, stats]) => {
                const displayName = stats.user.globalName || stats.user.username;
                const searchText = `${displayName} ${stats.user.username} ${userId}`.toLowerCase();

                return `
                    <div class="user-item" data-name="${searchText}">
                        <div class="user-info">
                            <div class="user-avatar">
                                ${stats.user.avatar ?
                                    `<img src="https://cdn.discordapp.com/avatars/${stats.user.id}/${stats.user.avatar}.png?size=32"
                                         style="width: 32px; height: 32px; border-radius: 50%;" />` :
                                    displayName.charAt(0).toUpperCase()
                                }
                            </div>
                            <div>
                                <div class="user-name">${displayName}</div>
                                <div style="font-size: 0.8em; color: #b9bbbe;">
                                    ${stats.user.username}${stats.user.discriminator !== '0' ? '#' + stats.user.discriminator : ''} | ID: ${userId}
                                </div>
                            </div>
                        </div>
                        <div class="user-count">${stats.count}</div>
                    </div>
                `;
            }).join('');
        }

        function renderGroups() {
            const groupsList = document.getElementById('groupsList');

            groupsList.innerHTML = data.groups.map(group => {
                const lastMessage = group.lastMessageTimestamp ?
                    new Date(group.lastMessageTimestamp).toLocaleDateString('ru-RU') : 'Неизвестно';

                return `
                    <div class="group-item" data-name="${group.name.toLowerCase()}">
                        <div class="group-name">${group.name}</div>
                        <div class="group-meta">
                            👥 ${group.participantCount} участников |
                            📅 Последнее сообщение: ${lastMessage} |
                            👑 Владелец: ${group.owner ? (group.owner.globalName || group.owner.username) : 'Неизвестно'}
                        </div>
                        <div class="participants">
                            ${group.recipients.map(p =>
                                `<span class="participant">${p.globalName || p.username}</span>`
                            ).join('')}
                        </div>
                    </div>
                `;
            }).join('');
        }

        function renderOwners() {
            const ownersList = document.getElementById('ownersList');
            const sortedOwners = Object.entries(ownerStats)
                .sort((a, b) => b[1].count - a[1].count);

            ownersList.innerHTML = sortedOwners.map(([userId, stats]) => {
                const displayName = stats.user.globalName || stats.user.username;
                const searchText = `${displayName} ${stats.user.username} ${userId}`.toLowerCase();

                return `
                    <div class="user-item" data-name="${searchText}">
                        <div class="user-info">
                            <div class="user-avatar">
                                ${stats.user.avatar ?
                                    `<img src="https://cdn.discordapp.com/avatars/${stats.user.id}/${stats.user.avatar}.png?size=32"
                                         style="width: 32px; height: 32px; border-radius: 50%;" />` :
                                    displayName.charAt(0).toUpperCase()
                                }
                            </div>
                            <div>
                                <div class="user-name">${displayName}</div>
                                <div style="font-size: 0.8em; color: #b9bbbe;">
                                    ${stats.user.username}${stats.user.discriminator !== '0' ? '#' + stats.user.discriminator : ''} | ID: ${userId}
                                </div>
                            </div>
                        </div>
                        <div class="user-count">${stats.count}</div>
                    </div>
                `;
            }).join('');
        }

        function switchTab(tabName) {
            // Убираем активный класс со всех табов
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));

            // Добавляем активный класс к выбранному табу
            event.target.classList.add('active');
            document.getElementById(tabName + '-panel').classList.add('active');
        }

        function filterUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();
            const userItems = document.querySelectorAll('#usersList .user-item');

            userItems.forEach(item => {
                const name = item.getAttribute('data-name');
                item.style.display = name.includes(searchTerm) ? 'flex' : 'none';
            });
        }

        function filterGroups() {
            const searchTerm = document.getElementById('groupSearch').value.toLowerCase();
            const groupItems = document.querySelectorAll('#groupsList .group-item');

            groupItems.forEach(item => {
                const name = item.getAttribute('data-name');
                item.style.display = name.includes(searchTerm) ? 'block' : 'none';
            });
        }

        function filterOwners() {
            const searchTerm = document.getElementById('ownerSearch').value.toLowerCase();
            const ownerItems = document.querySelectorAll('#ownersList .user-item');

            ownerItems.forEach(item => {
                const name = item.getAttribute('data-name');
                item.style.display = name.includes(searchTerm) ? 'flex' : 'none';
            });
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    </script>
</body>
</html>
