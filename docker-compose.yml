services:
  # PostgreSQL база данных
  postgres:
    image: postgres:15-alpine
    container_name: discord_groups_db
    environment:
      POSTGRES_DB: discord_groups
      POSTGRES_USER: discord_user
      POSTGRES_PASSWORD: discord_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - discord_network

  # Node.js API сервер
  api:
    build: .
    container_name: discord_groups_api
    environment:
      NODE_ENV: development
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: discord_groups
      DB_USER: discord_user
      DB_PASSWORD: discord_password
    depends_on:
      - postgres
    networks:
      - discord_network
    restart: unless-stopped

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: discord_groups_nginx
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - api
    networks:
      - discord_network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  discord_network:
    driver: bridge
