const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const fileInfo = document.getElementById('fileInfo');
const progress = document.getElementById('progress');
const progressBar = document.getElementById('progressBar');
const result = document.getElementById('result');

// Drag and drop
uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
});

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        handleFile(file);
    }
}

function handleFile(file) {
    if (!file.name.endsWith('.json')) {
        showResult('error', 'Пожалуйста, выберите JSON файл');
        return;
    }

    const sizeMB = (file.size / 1024 / 1024).toFixed(2);
    fileInfo.innerHTML = `📄 ${file.name} (${sizeMB} MB)`;
    
    uploadFile(file);
}

async function uploadFile(file) {
    progress.style.display = 'block';
    result.style.display = 'none';
    
    try {
        // Читаем файл как текст
        const text = await file.text();
        const data = JSON.parse(text);
        
        if (!data.groups || !Array.isArray(data.groups)) {
            showResult('error', '❌ Неверный формат файла. Ожидается объект с полем groups.');
            return;
        }
        
        const groups = data.groups;
        const metadata = data.metadata;
        const batchSize = 1000; // Обрабатываем по 1000 групп за раз
        const totalBatches = Math.ceil(groups.length / batchSize);
        
        console.log(`Всего групп: ${groups.length}, батчей: ${totalBatches}`);
        
        let totalImported = { users: 0, groups: 0, connections: 0 };
        
        // Отправляем батчи
        for (let i = 0; i < totalBatches; i++) {
            const start = i * batchSize;
            const end = Math.min(start + batchSize, groups.length);
            const batch = groups.slice(start, end);
            
            const batchData = {
                groups: batch,
                batchNumber: i + 1,
                totalBatches: totalBatches,
                metadata: i === 0 ? metadata : null
            };
            
            // Обновляем прогресс
            const progressPercent = ((i + 1) / totalBatches) * 100;
            progressBar.style.width = progressPercent + '%';
            
            // Отправляем батч
            const response = await fetch('/api/import/groups/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(batchData)
            });
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || error.error || 'Ошибка сервера');
            }
            
            const result = await response.json();
            totalImported.users += result.imported.users;
            totalImported.groups += result.imported.groups;
            totalImported.connections += result.imported.connections;
            
            console.log(`Батч ${i + 1}/${totalBatches} обработан`);
        }
        
        progress.style.display = 'none';
        showResult('success', `
            ✅ Импорт завершен успешно!<br>
            👥 Пользователей: ${totalImported.users}<br>
            👥 Групп: ${totalImported.groups}<br>
            🔗 Связей: ${totalImported.connections}<br>
            📊 Обработано групп: ${groups.length}<br>
            📦 Батчей: ${totalBatches}
        `);
        
    } catch (error) {
        progress.style.display = 'none';
        showResult('error', `❌ Ошибка: ${error.message}`);
    }
}

function showResult(type, message) {
    result.className = `result ${type}`;
    result.innerHTML = message;
    result.style.display = 'block';
}

function selectFile() {
    document.getElementById('fileInput').click();
}
