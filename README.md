# Discord Groups API Server

Локальный сервер для хранения и получения информации о группах Discord пользователей.

## 🚀 Быстрый старт

### Требования
- [Docker Desktop](https://www.docker.com/products/docker-desktop) (Windows/Mac/Linux)

### Запуск
1. **Скачайте все файлы** в одну папку
2. **Запустите сервер:**
   ```bash
   # Windows
   start.bat
   
   # Linux/Mac
   chmod +x start.sh && ./start.sh
   ```

3. **Проверьте работу:** http://localhost/api/stats

## 📊 API Эндпоинты

### Получить группы пользователя
```http
GET /api/user/{userId}/groups?limit=50&offset=0&search=название
```

**Пример:**
```bash
curl "http://localhost/api/user/123456789012345678/groups?limit=10"
```

**Ответ:**
```json
{
  "groups": [
    {
      "group_id": "111111111111111111",
      "group_name": "Тестовая группа 1",
      "group_description": "Описание группы",
      "member_count": 3,
      "joined_at": "2024-01-15T10:30:00.000Z",
      "role": "owner"
    }
  ],
  "total": 1,
  "limit": 10,
  "offset": 0,
  "hasMore": false
}
```

### Получить пользователей группы
```http
GET /api/group/{groupId}/users?limit=100&offset=0
```

### Поиск групп
```http
GET /api/search/groups?q=заказы&limit=20
```

### Статистика
```http
GET /api/stats
```

### Импорт данных
```http
POST /api/import/groups
Content-Type: application/json

{
  "users": [
    {
      "id": "123456789012345678",
      "username": "testuser",
      "display_name": "Test User"
    }
  ],
  "groups": [
    {
      "id": "111111111111111111",
      "name": "Группа заказов",
      "description": "Описание группы",
      "owner_id": "123456789012345678",
      "members": [
        {
          "user_id": "123456789012345678",
          "role": "owner"
        }
      ]
    }
  ]
}
```

## 🗄️ База данных

### Подключение к PostgreSQL
```bash
# Параметры подключения
Host: localhost
Port: 5432
Database: discord_groups
User: discord_user
Password: discord_password
```

### Структура таблиц
- **users** - информация о пользователях
- **groups** - информация о группах
- **user_groups** - связи пользователь-группа

## 🔧 Управление

### Просмотр логов
```bash
# Логи API сервера
docker-compose logs -f api

# Логи базы данных
docker-compose logs -f postgres

# Логи Nginx
docker-compose logs -f nginx
```

### Остановка сервера
```bash
docker-compose down
```

### Полная очистка (удаление данных)
```bash
docker-compose down -v
docker system prune -f
```

### Резервное копирование БД
```bash
docker exec discord_groups_db pg_dump -U discord_user discord_groups > backup.sql
```

### Восстановление БД
```bash
docker exec -i discord_groups_db psql -U discord_user discord_groups < backup.sql
```

## 📈 Производительность

### Оптимизация для больших объемов данных:
- **Индексы** на user_id, group_id для быстрого поиска
- **Кэширование** запросов на 5 минут
- **Пагинация** для больших результатов
- **Gzip сжатие** для уменьшения трафика

### Мониторинг:
```bash
# Использование ресурсов
docker stats

# Размер базы данных
docker exec discord_groups_db psql -U discord_user -d discord_groups -c "
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
"
```

## 🔒 Безопасность

- CORS настроен только для Discord доменов
- Helmet.js для базовой защиты
- Ограничение размера запросов (10MB)
- Prepared statements против SQL инъекций

## 🐛 Устранение неполадок

### Порты заняты
```bash
# Проверить, что использует порт
netstat -ano | findstr :3000
netstat -ano | findstr :5432
netstat -ano | findstr :80
```

### Контейнеры не запускаются
```bash
# Проверить логи
docker-compose logs

# Пересобрать контейнеры
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### База данных недоступна
```bash
# Проверить статус PostgreSQL
docker-compose exec postgres pg_isready -U discord_user

# Подключиться к БД напрямую
docker-compose exec postgres psql -U discord_user -d discord_groups
```
