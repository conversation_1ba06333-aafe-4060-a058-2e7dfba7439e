@echo off
cd /d "%~dp0"
echo 🔄 Перезапуск Discord Groups API сервера...
echo.

echo 📁 Рабочая папка: %CD%
echo.

echo 🛑 Останавливаем все контейнеры...
docker-compose down

echo.
echo 🗑️ Удаляем старые образы...
docker-compose down --rmi all

echo.
echo 🔨 Пересобираем контейнеры без кэша...
docker-compose build --no-cache

echo.
echo 🚀 Запускаем контейнеры...
docker-compose up -d

echo.
echo ⏳ Ждем запуска сервисов...
timeout /t 15 /nobreak >nul

echo.
echo 📊 Проверяем статус контейнеров:
docker-compose ps

echo.
echo 📋 Проверяем логи API:
docker-compose logs --tail=20 api

echo.
echo 🎉 Перезапуск завершен!
echo.
echo 📍 Проверьте работу: http://localhost/api/stats
echo.

pause
