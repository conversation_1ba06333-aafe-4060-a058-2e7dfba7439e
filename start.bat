@echo off
cd /d "%~dp0"
echo 🚀 Запуск Discord Groups API сервера...
echo.

echo 📁 Рабочая папка: %CD%
echo.

REM Проверяем, установлен ли Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker не установлен или не запущен!
    echo Установите Docker Desktop: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ Docker найден
echo.

REM Останавливаем предыдущие контейнеры (если есть)
echo 🛑 Останавливаем предыдущие контейнеры...
docker-compose down

echo.
echo 🔨 Собираем и запускаем контейнеры...
docker-compose up --build -d

echo.
echo ⏳ Ждем запуска сервисов...
timeout /t 10 /nobreak >nul

echo.
echo 📊 Проверяем статус контейнеров:
docker-compose ps

echo.
echo 🎉 Сервер запущен!
echo.
echo 📍 Доступные адреса:
echo   • API (через Nginx): http://localhost
echo   • API (прямой): http://localhost:3001
echo   • База данных: localhost:5432
echo.
echo 📊 Полезные команды:
echo   • Статистика: curl http://localhost/api/stats
echo   • Логи API: docker-compose logs -f api
echo   • Логи БД: docker-compose logs -f postgres
echo.
echo 🛑 Для остановки: docker-compose down
echo.

REM Открываем браузер со статистикой
start http://localhost/api/stats

pause
