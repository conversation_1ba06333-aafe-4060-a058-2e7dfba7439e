{"name": "discord-groups-api", "version": "1.0.0", "description": "API для получения информации о группах Discord пользователей", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["discord", "groups", "api"], "author": "YourName", "license": "MIT", "dependencies": {"express": "^4.18.2", "pg": "^8.11.3", "cors": "^2.8.5", "node-cache": "^5.1.2", "helmet": "^7.1.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "stream-json": "^1.8.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1"}}