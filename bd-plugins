# UserGroupsAPI Plugin для BetterDiscord

Плагин для BetterDiscord, который позволяет просматривать информацию о группах пользователей через API сервер.

## 🚀 Возможности

- **Поиск групп** по названию
- **Просмотр групп пользователя** через контекстное меню
- **Просмотр участников группы** с аватарами и ролями
- **Статистика базы данных** в реальном времени
- **Настраиваемый API URL** и таймауты

## 📦 Установка

### Предварительные требования

1. **BetterDiscord** должен быть установлен
2. **ZeresPluginLibrary** должна быть установлена (плагин предложит скачать автоматически)
3. **API сервер** должен быть запущен (см. основной README)

### Установка плагина

1. Скачайте файл `UserGroupsAPI.plugin.js`
2. Поместите его в папку плагинов BetterDiscord:
   - **Windows**: `%appdata%\BetterDiscord\plugins\`
   - **macOS**: `~/Library/Application Support/BetterDiscord/plugins/`
   - **Linux**: `~/.config/BetterDiscord/plugins/`
3. Перезапустите Discord или включите плагин в настройках BetterDiscord

## ⚙️ Настройка

1. Откройте настройки BetterDiscord
2. Перейдите в раздел "Плагины"
3. Найдите "UserGroupsAPI" и нажмите на шестеренку
4. Настройте параметры:
   - **URL API сервера**: `http://localhost:8080` (по умолчанию)
   - **Показывать уведомления**: включить/выключить уведомления об ошибках
   - **Таймаут запросов**: время ожидания ответа от API (5-60 секунд)

## 🎯 Использование

### Основное окно

1. Нажмите на иконку 🔍 в панели инструментов Discord
2. Откроется окно с двумя вкладками:
   - **Поиск групп**: поиск по названию группы
   - **Статистика**: информация о базе данных

### Поиск групп

1. Введите название группы в поле поиска
2. Нажмите "Найти" или Enter
3. Кликните на группу, чтобы посмотреть участников

### Просмотр групп пользователя

1. Кликните правой кнопкой на любого пользователя
2. Выберите "Показать группы (API)"
3. Откроется список групп пользователя
4. Кликните на группу, чтобы посмотреть участников

### Просмотр участников группы

- В окне участников отображаются:
  - Аватары пользователей
  - Имена и никнейм
  - ID пользователей
  - Роли в группе (owner/member)

## 🔧 API Эндпоинты

Плагин использует следующие эндпоинты API:

- `GET /api/user/{userId}/groups` - получить группы пользователя
- `GET /api/group/{groupId}/users` - получить участников группы
- `GET /api/search/groups?q={query}` - поиск групп
- `GET /api/stats` - статистика базы данных

## 🐛 Устранение неполадок

### Плагин не загружается

1. Убедитесь, что установлена ZeresPluginLibrary
2. Проверьте консоль Discord (Ctrl+Shift+I) на наличие ошибок
3. Перезапустите Discord

### Ошибки API

1. Проверьте, что API сервер запущен на указанном URL
2. Убедитесь, что сервер доступен (попробуйте открыть URL в браузере)
3. Проверьте настройки CORS на сервере
4. Увеличьте таймаут запросов в настройках плагина

### Пустые результаты

1. Убедитесь, что данные были импортированы в базу данных
2. Проверьте статистику в плагине - должны быть пользователи и группы
3. Попробуйте поиск с другими ключевыми словами

## 📊 Статистика

В разделе статистики отображается:
- Количество пользователей в базе
- Количество групп в базе  
- Количество связей пользователь-группа
- Время последнего обновления

## 🔒 Безопасность

- Плагин не собирает и не отправляет личные данные
- Все запросы идут только к указанному API серверу
- Данные кэшируются только локально в Discord

## 📝 Логирование

Плагин записывает логи в консоль Discord:
- Информационные сообщения о запуске/остановке
- Ошибки API запросов
- Отладочная информация

Для просмотра логов откройте консоль разработчика (Ctrl+Shift+I).

## 🆕 Обновления

Плагин автоматически проверяет обновления при запуске Discord. Для ручного обновления:

1. Скачайте новую версию плагина
2. Замените старый файл в папке плагинов
3. Перезапустите Discord

## 📞 Поддержка

При возникновении проблем:

1. Проверьте этот README
2. Посмотрите логи в консоли Discord
3. Убедитесь, что API сервер работает корректно
4. Создайте issue в репозитории проекта

## 📄 Лицензия

MIT License - см. основной README проекта.
