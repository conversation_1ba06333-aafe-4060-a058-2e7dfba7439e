<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Загрузка Discord Groups</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        input[type="file"] {
            display: none;
        }
        .upload-btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .file-info {
            margin: 10px 0;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Загрузка Discord Groups</h1>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Перетащите JSON файл сюда или</p>
            <button class="upload-btn" id="selectFileBtn">
                Выберите файл
            </button>
            <input type="file" id="fileInput" accept=".json">
            <div class="file-info" id="fileInfo"></div>
        </div>

        <div class="progress" id="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div class="result" id="result"></div>

        <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 5px;">
            <h3>📋 Инструкция:</h3>
            <ol>
                <li>Выберите JSON файл с данными Discord групп</li>
                <li>Файл будет загружен и обработан потоково</li>
                <li>Поддерживаются файлы до 1GB</li>
                <li>Процесс может занять несколько минут для больших файлов</li>
            </ol>
        </div>
    </div>

    <script src="/upload.js"></script>
    <script>
        // Инициализация после загрузки DOM
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('selectFileBtn').addEventListener('click', selectFile);
            document.getElementById('fileInput').addEventListener('change', handleFileSelect);
        });
    </script>
</body>
</html>
