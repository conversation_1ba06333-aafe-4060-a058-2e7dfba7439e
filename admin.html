<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Админ-панель - Discord Groups API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 800px;
            margin: 20px;
        }

        .header {
            background: #5865f2;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .content {
            padding: 30px;
        }

        .login-form {
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #5865f2;
        }

        .btn {
            background: #5865f2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #4752c4;
        }

        .btn-danger {
            background: #f04747;
        }

        .btn-danger:hover {
            background: #d73527;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 14px;
        }

        .admin-panel {
            display: none;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-card h3 {
            color: #5865f2;
            margin-bottom: 10px;
        }

        .stat-card .number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .ignored-users {
            margin-top: 30px;
        }

        .ignored-users h2 {
            margin-bottom: 20px;
            color: #333;
        }

        .user-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .user-form h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .users-list {
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
        }

        .user-item {
            padding: 15px;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .user-item:last-child {
            border-bottom: none;
        }

        .user-info h4 {
            margin-bottom: 5px;
            color: #333;
        }

        .user-info p {
            color: #666;
            font-size: 14px;
            margin-bottom: 2px;
        }

        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        /* Стили для вкладок */
        .tabs {
            display: flex;
            border-bottom: 2px solid #e1e5e9;
            margin-bottom: 30px;
        }

        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .tab:hover {
            color: #5865f2;
            background: #f8f9fa;
        }

        .tab.active {
            color: #5865f2;
            border-bottom-color: #5865f2;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Стили для статистики пользователей */
        .user-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .user-stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #5865f2;
        }



        .user-stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .user-stat-name {
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }

        .user-stat-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .user-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .search-box:focus {
            outline: none;
            border-color: #5865f2;
        }

        @media (max-width: 600px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .stats {
                grid-template-columns: 1fr;
            }

            .tabs {
                flex-direction: column;
            }

            .user-stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ Админ-панель</h1>
            <p>Управление игнорируемыми пользователями Discord Groups API</p>
        </div>

        <div class="content">
            <!-- Форма входа -->
            <div id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="password">Пароль администратора:</label>
                    <input type="password" id="password" placeholder="Введите пароль">
                </div>
                <button class="btn" onclick="login()">Войти</button>
            </div>

            <!-- Админ-панель -->
            <div id="adminPanel" class="admin-panel">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h2>Панель управления</h2>
                    <button class="btn btn-danger" onclick="logout()">Выйти</button>
                </div>

                <div id="alerts"></div>

                <!-- Кнопка миграции -->
                <div style="margin-bottom: 20px; text-align: center;">
                    <button class="btn" onclick="runMigration()" style="background: #f04747;">
                        🔧 Выполнить миграцию БД (создать таблицы ignored_users и color_settings)
                    </button>
                </div>

                <!-- Статистика -->
                <div class="stats">
                    <div class="stat-card">
                        <h3>👥 Пользователи</h3>
                        <div class="number" id="usersCount">-</div>
                    </div>
                    <div class="stat-card">
                        <h3>💬 Группы</h3>
                        <div class="number" id="groupsCount">-</div>
                    </div>
                    <div class="stat-card">
                        <h3>🚫 Игнорируемые</h3>
                        <div class="number" id="ignoredCount">-</div>
                    </div>
                </div>

                <!-- Вкладки -->
                <div class="tabs">
                    <button class="tab active" onclick="switchTab('statistics')">📊 Статистика пользователей</button>
                    <button class="tab" onclick="switchTab('colors')">🎨 Настройки цветов</button>
                    <button class="tab" onclick="switchTab('ignored')">🚫 Игнорируемые</button>
                </div>

                <!-- Вкладка статистики пользователей -->
                <div id="statisticsTab" class="tab-content active">
                    <h2>📊 Статистика пользователей</h2>

                    <!-- Поиск пользователей -->
                    <input type="text" id="userSearch" class="search-box" placeholder="🔍 Поиск пользователей по имени или ID..." onkeyup="filterUsers()">

                    <!-- Сортировка -->
                    <div style="margin-bottom: 20px;">
                        <label for="sortBy" style="margin-right: 10px;">Сортировка:</label>
                        <select id="sortBy" onchange="sortUsers()" style="padding: 8px; border-radius: 4px; border: 1px solid #e1e5e9;">
                            <option value="groups_desc">По количеству групп (убыв.)</option>
                            <option value="groups_asc">По количеству групп (возр.)</option>
                            <option value="name_asc">По имени (А-Я)</option>
                            <option value="name_desc">По имени (Я-А)</option>
                        </select>
                    </div>

                    <!-- Список пользователей -->
                    <div id="userStatsList" class="user-stats-grid">
                        <div class="loading">Загрузка статистики пользователей...</div>
                    </div>
                </div>

                <!-- Вкладка настроек цветов -->
                <div id="colorsTab" class="tab-content">
                    <h2>🎨 Настройки цветов окантовки</h2>

                    <!-- Форма добавления новой настройки -->
                    <div class="user-form">
                        <h3>Добавить настройку цвета</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="colorMinGroups">Минимум групп *:</label>
                                <input type="number" id="colorMinGroups" placeholder="1" min="0">
                            </div>
                            <div class="form-group">
                                <label for="colorMaxGroups">Максимум групп:</label>
                                <input type="number" id="colorMaxGroups" placeholder="Оставьте пустым для ∞" min="1">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="colorValue">Цвет *:</label>
                                <input type="color" id="colorValue" value="#43b581">
                            </div>
                            <div class="form-group">
                                <label for="colorDescription">Описание:</label>
                                <input type="text" id="colorDescription" placeholder="Описание цвета">
                            </div>
                        </div>
                        <button class="btn" onclick="addColorSetting()">Добавить настройку</button>
                    </div>

                    <!-- Список настроек цветов -->
                    <div class="users-list" id="colorSettingsList">
                        <div class="loading">Загрузка...</div>
                    </div>
                </div>

                <!-- Вкладка игнорируемых пользователей -->
                <div id="ignoredTab" class="tab-content">
                    <h2>🚫 Игнорируемые пользователи</h2>
                    
                    <!-- Форма добавления -->
                    <div class="user-form">
                        <h3>Добавить пользователя</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="userId">ID пользователя *:</label>
                                <input type="text" id="userId" placeholder="123456789012345678">
                            </div>
                            <div class="form-group">
                                <label for="username">Имя пользователя:</label>
                                <input type="text" id="username" placeholder="username">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="displayName">Отображаемое имя:</label>
                                <input type="text" id="displayName" placeholder="Display Name">
                            </div>
                            <div class="form-group">
                                <label for="reason">Причина:</label>
                                <input type="text" id="reason" placeholder="Причина игнорирования">
                            </div>
                        </div>
                        <button class="btn" onclick="addIgnoredUser()">Добавить</button>
                    </div>

                    <!-- Список игнорируемых пользователей -->
                    <div class="users-list" id="usersList">
                        <div class="loading">Загрузка...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isLoggedIn = false;

        // Проверка авторизации при загрузке страницы
        window.onload = function() {
            checkAuth();
        };

        async function checkAuth() {
            try {
                const response = await fetch('/admin/check');
                const data = await response.json();
                
                if (data.isAdmin) {
                    showAdminPanel();
                } else {
                    showLoginForm();
                }
            } catch (error) {
                console.error('Ошибка проверки авторизации:', error);
                showLoginForm();
            }
        }

        async function login() {
            const password = document.getElementById('password').value;
            
            if (!password) {
                showAlert('Введите пароль', 'error');
                return;
            }

            try {
                const response = await fetch('/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ password })
                });

                const data = await response.json();

                if (data.success) {
                    showAdminPanel();
                } else {
                    showAlert(data.error || 'Неверный пароль', 'error');
                }
            } catch (error) {
                console.error('Ошибка входа:', error);
                showAlert('Ошибка подключения к серверу', 'error');
            }
        }

        async function logout() {
            try {
                await fetch('/admin/logout', { method: 'POST' });
                showLoginForm();
            } catch (error) {
                console.error('Ошибка выхода:', error);
            }
        }

        function showLoginForm() {
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('adminPanel').style.display = 'none';
            document.getElementById('password').value = '';
            isLoggedIn = false;
        }

        function showAdminPanel() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('adminPanel').style.display = 'block';
            isLoggedIn = true;
            loadData();
        }

        let allUsers = [];
        let filteredUsers = [];

        async function loadData() {
            await Promise.all([
                loadStats(),
                loadUserStats(),
                loadIgnoredUsers(),
                loadColorSettings()
            ]);
        }

        function switchTab(tabName) {
            // Убираем активный класс со всех вкладок и контента
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Добавляем активный класс к выбранной вкладке
            event.target.classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');

            // Загружаем данные для вкладки при необходимости
            if (tabName === 'statistics' && allUsers.length === 0) {
                loadUserStats();
            }
        }

        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                document.getElementById('usersCount').textContent = data.users || 0;
                document.getElementById('groupsCount').textContent = data.groups || 0;
            } catch (error) {
                console.error('Ошибка загрузки статистики:', error);
            }
        }

        async function loadUserStats() {
            try {
                const response = await fetch('/api/admin/user-stats?limit=500');
                const data = await response.json();

                if (data.success) {
                    allUsers = data.users;
                    filteredUsers = [...allUsers];
                    displayUserStats(filteredUsers);
                } else {
                    showAlert('Ошибка загрузки статистики пользователей', 'error');
                }
            } catch (error) {
                console.error('Ошибка загрузки статистики пользователей:', error);
                showAlert('Ошибка подключения к серверу', 'error');
            }
        }

        async function loadIgnoredUsers() {
            try {
                const response = await fetch('/api/ignored-users');
                const data = await response.json();

                if (data.success) {
                    displayIgnoredUsers(data.users);
                    document.getElementById('ignoredCount').textContent = data.users.length;
                } else {
                    showAlert('Ошибка загрузки игнорируемых пользователей', 'error');
                }
            } catch (error) {
                console.error('Ошибка загрузки игнорируемых пользователей:', error);
                showAlert('Ошибка подключения к серверу', 'error');
            }
        }

        function displayUserStats(users) {
            const userStatsList = document.getElementById('userStatsList');

            if (users.length === 0) {
                userStatsList.innerHTML = '<div class="loading">Пользователи не найдены</div>';
                return;
            }

            userStatsList.innerHTML = users.map(user => `
                <div class="user-stat-card">
                    <div class="user-stat-header">
                        <div class="user-stat-name">
                            ${user.display_name || user.username || 'Неизвестно'}
                        </div>
                        <div style="font-weight: bold; color: ${getColorForGroups(user.group_count)};">
                            ${user.group_count} групп
                        </div>
                    </div>
                    <div class="user-stat-info">
                        <strong>ID:</strong> ${user.user_id}
                    </div>
                    ${user.username ? `<div class="user-stat-info"><strong>Username:</strong> ${user.username}</div>` : ''}
                    ${user.last_seen ? `<div class="user-stat-info"><strong>Последняя активность:</strong> ${new Date(user.last_seen).toLocaleString('ru-RU')}</div>` : ''}
                    <div class="user-actions">
                        <button class="btn btn-danger btn-small" onclick="addToIgnoreFromStats('${user.user_id}', '${user.username || ''}', '${user.display_name || ''}')">
                            Добавить в игнор
                        </button>
                        <button class="btn btn-small" onclick="showUserGroups('${user.user_id}')">
                            Показать группы
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function getColorForGroups(count) {
            if (count >= 10) return '#7289da';
            if (count >= 5) return '#f04747';
            if (count >= 2) return '#faa61a';
            if (count >= 1) return '#43b581';
            return '#666';
        }

        function filterUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();

            if (!searchTerm) {
                filteredUsers = [...allUsers];
            } else {
                filteredUsers = allUsers.filter(user =>
                    (user.username && user.username.toLowerCase().includes(searchTerm)) ||
                    (user.display_name && user.display_name.toLowerCase().includes(searchTerm)) ||
                    user.user_id.toString().includes(searchTerm)
                );
            }

            sortUsers();
        }

        function sortUsers() {
            const sortBy = document.getElementById('sortBy').value;

            filteredUsers.sort((a, b) => {
                switch (sortBy) {
                    case 'groups_desc':
                        return b.group_count - a.group_count;
                    case 'groups_asc':
                        return a.group_count - b.group_count;
                    case 'name_asc':
                        return (a.display_name || a.username || '').localeCompare(b.display_name || b.username || '');
                    case 'name_desc':
                        return (b.display_name || b.username || '').localeCompare(a.display_name || a.username || '');
                    default:
                        return 0;
                }
            });

            displayUserStats(filteredUsers);
        }

        async function addToIgnoreFromStats(userId, username, displayName) {
            const reason = prompt('Причина игнорирования (необязательно):');
            if (reason === null) return; // Пользователь отменил

            try {
                const response = await fetch('/api/ignored-users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        userId,
                        username: username || null,
                        displayName: displayName || null,
                        reason: reason || null
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert('Пользователь добавлен в игнорируемые и скрыт из статистики', 'success');

                    // Удаляем пользователя из локальных массивов
                    allUsers = allUsers.filter(user => user.user_id !== userId);
                    filteredUsers = filteredUsers.filter(user => user.user_id !== userId);

                    // Обновляем отображение
                    displayUserStats(filteredUsers);
                    loadIgnoredUsers(); // Обновляем счетчик игнорируемых
                } else {
                    showAlert(data.error || 'Ошибка добавления пользователя', 'error');
                }
            } catch (error) {
                console.error('Ошибка добавления пользователя:', error);
                showAlert('Ошибка подключения к серверу', 'error');
            }
        }



        async function showUserGroups(userId) {
            try {
                const response = await fetch(`/api/user/${userId}/groups`);
                const data = await response.json();

                if (data.groups && data.groups.length > 0) {
                    const groupsList = data.groups.map(group =>
                        `• ${group.group_name || 'Без названия'} (${group.member_count} участников)`
                    ).join('\n');

                    alert(`Группы пользователя:\n\n${groupsList}`);
                } else {
                    alert('Пользователь не состоит в группах');
                }
            } catch (error) {
                console.error('Ошибка получения групп пользователя:', error);
                showAlert('Ошибка получения групп пользователя', 'error');
            }
        }

        function displayIgnoredUsers(users) {
            const usersList = document.getElementById('usersList');
            
            if (users.length === 0) {
                usersList.innerHTML = '<div class="loading">Нет игнорируемых пользователей</div>';
                return;
            }

            usersList.innerHTML = users.map(user => `
                <div class="user-item">
                    <div class="user-info">
                        <h4>${user.display_name || user.username || 'Неизвестно'}</h4>
                        <p><strong>ID:</strong> ${user.user_id}</p>
                        ${user.username ? `<p><strong>Username:</strong> ${user.username}</p>` : ''}
                        ${user.reason ? `<p><strong>Причина:</strong> ${user.reason}</p>` : ''}
                        <p><strong>Добавлен:</strong> ${new Date(user.added_at).toLocaleString('ru-RU')}</p>
                    </div>
                    <button class="btn btn-danger btn-small" onclick="removeIgnoredUser('${user.user_id}')">
                        Удалить
                    </button>
                </div>
            `).join('');
        }

        async function addIgnoredUser() {
            const userId = document.getElementById('userId').value.trim();
            const username = document.getElementById('username').value.trim();
            const displayName = document.getElementById('displayName').value.trim();
            const reason = document.getElementById('reason').value.trim();

            if (!userId) {
                showAlert('ID пользователя обязателен', 'error');
                return;
            }

            if (!/^\d{17,19}$/.test(userId)) {
                showAlert('ID пользователя должен содержать 17-19 цифр', 'error');
                return;
            }

            try {
                const response = await fetch('/api/ignored-users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        userId,
                        username: username || null,
                        displayName: displayName || null,
                        reason: reason || null
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert('Пользователь добавлен в игнорируемые', 'success');
                    // Очищаем форму
                    document.getElementById('userId').value = '';
                    document.getElementById('username').value = '';
                    document.getElementById('displayName').value = '';
                    document.getElementById('reason').value = '';
                    // Перезагружаем список
                    loadIgnoredUsers();
                } else {
                    showAlert(data.error || 'Ошибка добавления пользователя', 'error');
                }
            } catch (error) {
                console.error('Ошибка добавления пользователя:', error);
                showAlert('Ошибка подключения к серверу', 'error');
            }
        }

        async function removeIgnoredUser(userId) {
            if (!confirm('Вы уверены, что хотите удалить этого пользователя из игнорируемых?')) {
                return;
            }

            try {
                const response = await fetch(`/api/ignored-users/${userId}`, {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (data.success) {
                    showAlert('Пользователь удален из игнорируемых', 'success');
                    loadIgnoredUsers();
                } else {
                    showAlert(data.error || 'Ошибка удаления пользователя', 'error');
                }
            } catch (error) {
                console.error('Ошибка удаления пользователя:', error);
                showAlert('Ошибка подключения к серверу', 'error');
            }
        }

        async function runMigration() {
            if (!confirm('Выполнить миграцию базы данных? Это создаст таблицы ignored_users и color_settings.')) {
                return;
            }

            try {
                const response = await fetch('/api/migrate/add-ignored-users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    showAlert('Миграция выполнена успешно!', 'success');
                    loadData(); // Перезагружаем данные
                } else {
                    showAlert(data.error || 'Ошибка выполнения миграции', 'error');
                }
            } catch (error) {
                console.error('Ошибка миграции:', error);
                showAlert('Ошибка подключения к серверу', 'error');
            }
        }

        async function loadColorSettings() {
            try {
                const response = await fetch('/api/admin/color-settings');
                const data = await response.json();

                if (data.success) {
                    displayColorSettings(data.colorSettings);
                } else {
                    showAlert('Ошибка загрузки настроек цветов', 'error');
                }
            } catch (error) {
                console.error('Ошибка загрузки настроек цветов:', error);
                showAlert('Ошибка подключения к серверу', 'error');
            }
        }

        function displayColorSettings(settings) {
            const settingsList = document.getElementById('colorSettingsList');

            if (settings.length === 0) {
                settingsList.innerHTML = '<div class="loading">Нет настроек цветов</div>';
                return;
            }

            settingsList.innerHTML = settings.map(setting => `
                <div class="user-item">
                    <div class="user-info">
                        <h4 style="display: flex; align-items: center; gap: 10px;">
                            <span style="width: 20px; height: 20px; background: ${setting.color}; border-radius: 50%; display: inline-block;"></span>
                            ${setting.description || 'Без описания'}
                        </h4>
                        <p><strong>Диапазон:</strong> ${setting.min_groups}${setting.max_groups ? ` - ${setting.max_groups}` : '+'} групп</p>
                        <p><strong>Цвет:</strong> ${setting.color}</p>
                        <p><strong>Обновлено:</strong> ${new Date(setting.updated_at).toLocaleString('ru-RU')}</p>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button class="btn btn-small" onclick="editColorSetting(${setting.id}, '${setting.min_groups}', '${setting.max_groups || ''}', '${setting.color}', '${setting.description || ''}')">
                            Изменить
                        </button>
                        <button class="btn btn-danger btn-small" onclick="removeColorSetting(${setting.id})">
                            Удалить
                        </button>
                    </div>
                </div>
            `).join('');
        }

        async function addColorSetting() {
            const minGroups = parseInt(document.getElementById('colorMinGroups').value);
            const maxGroups = document.getElementById('colorMaxGroups').value ? parseInt(document.getElementById('colorMaxGroups').value) : null;
            const color = document.getElementById('colorValue').value;
            const description = document.getElementById('colorDescription').value.trim();

            if (!minGroups || minGroups < 0) {
                showAlert('Минимальное количество групп должно быть числом >= 0', 'error');
                return;
            }

            if (maxGroups && maxGroups < minGroups) {
                showAlert('Максимальное количество групп должно быть больше минимального', 'error');
                return;
            }

            try {
                const response = await fetch('/api/admin/color-settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        minGroups,
                        maxGroups,
                        color,
                        description: description || null
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert('Настройка цвета добавлена', 'success');
                    // Очищаем форму
                    document.getElementById('colorMinGroups').value = '';
                    document.getElementById('colorMaxGroups').value = '';
                    document.getElementById('colorValue').value = '#43b581';
                    document.getElementById('colorDescription').value = '';
                    // Перезагружаем список
                    loadColorSettings();
                } else {
                    showAlert(data.error || 'Ошибка добавления настройки', 'error');
                }
            } catch (error) {
                console.error('Ошибка добавления настройки:', error);
                showAlert('Ошибка подключения к серверу', 'error');
            }
        }

        async function editColorSetting(id, minGroups, maxGroups, color, description) {
            const newMinGroups = prompt('Минимальное количество групп:', minGroups);
            if (newMinGroups === null) return;

            const newMaxGroups = prompt('Максимальное количество групп (оставьте пустым для ∞):', maxGroups);
            if (newMaxGroups === null) return;

            const newColor = prompt('Цвет (hex):', color);
            if (newColor === null) return;

            const newDescription = prompt('Описание:', description);
            if (newDescription === null) return;

            try {
                const response = await fetch(`/api/admin/color-settings/${id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        minGroups: parseInt(newMinGroups),
                        maxGroups: newMaxGroups ? parseInt(newMaxGroups) : null,
                        color: newColor,
                        description: newDescription || null
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert('Настройка цвета обновлена', 'success');
                    loadColorSettings();
                } else {
                    showAlert(data.error || 'Ошибка обновления настройки', 'error');
                }
            } catch (error) {
                console.error('Ошибка обновления настройки:', error);
                showAlert('Ошибка подключения к серверу', 'error');
            }
        }

        async function removeColorSetting(id) {
            if (!confirm('Вы уверены, что хотите удалить эту настройку цвета?')) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/color-settings/${id}`, {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (data.success) {
                    showAlert('Настройка цвета удалена', 'success');
                    loadColorSettings();
                } else {
                    showAlert(data.error || 'Ошибка удаления настройки', 'error');
                }
            } catch (error) {
                console.error('Ошибка удаления настройки:', error);
                showAlert('Ошибка подключения к серверу', 'error');
            }
        }

        function showAlert(message, type) {
            const alertsContainer = document.getElementById('alerts');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass}`;
            alertDiv.textContent = message;

            alertsContainer.innerHTML = '';
            alertsContainer.appendChild(alertDiv);

            // Автоматически скрываем через 5 секунд
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // Обработка Enter в поле пароля
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    login();
                }
            });
        });
    </script>
</body>
</html>
