const express = require('express');
const { Pool } = require('pg');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const NodeCache = require('node-cache');
const multer = require('multer');
const StreamValues = require('stream-json/streamers/StreamValues');
const parser = require('stream-json');
const session = require('express-session');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Админский пароль для управления игнорируемыми пользователями
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin123';

// Настройка подключения к базе данных
const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'discord_groups',
    user: process.env.DB_USER || 'discord_user',
    password: process.env.DB_PASSWORD || 'discord_password',
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
});

// Кэш для запросов (5 минут)
const cache = new NodeCache({ stdTTL: 300, checkperiod: 60 });

// Middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
}));
app.use(compression());
// Простые и надежные CORS настройки
// app.use(cors({
//     origin: '*', // Разрешаем все домены для простоты
//     credentials: false,
//     methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
//     allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
//     optionsSuccessStatus: 200
// }));

// Настройка сессий для админ-панели
app.use(session({
    secret: process.env.SESSION_SECRET || 'discord-groups-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // Для HTTP (в продакшене должно быть true для HTTPS)
        maxAge: 24 * 60 * 60 * 1000 // 24 часа
    }
}));

app.use(express.json({
    limit: '500mb',
    parameterLimit: 100000,
    extended: true
}));
app.use(express.urlencoded({
    limit: '500mb',
    extended: true,
    parameterLimit: 100000
}));

// Middleware для логирования
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    if (req.method === 'POST') {
        console.log('POST Headers:', req.headers);
        console.log('POST Body type:', typeof req.body);
        console.log('POST Body:', req.body);
        console.log('🔄 Вызываем next() в middleware...');
    }
    next();
    if (req.method === 'POST') {
        console.log('✅ next() выполнен в middleware');
    }
});

// Проверка подключения к БД
pool.on('connect', () => {
    console.log('✅ Подключение к PostgreSQL установлено');
});

pool.on('error', (err) => {
    console.error('❌ Ошибка подключения к PostgreSQL:', err);
});

// Маршруты API

// ТЕСТОВЫЙ ЭНДПОИНТ - САМЫЙ ПЕРВЫЙ
app.get('/api/test', (req, res) => {
    console.log('🔥 ТЕСТОВЫЙ ЭНДПОИНТ СРАБОТАЛ!');
    res.json({ message: 'Тест работает!', timestamp: new Date().toISOString() });
});

// Миграция для добавления channel_id
app.post('/api/migrate/add-channel-id', async (req, res) => {
    try {
        const client = await pool.connect();

        try {
            await client.query('BEGIN');

            console.log('🔧 Выполняем миграцию channel_id...');

            // Добавляем поле channel_id
            await client.query('ALTER TABLE groups ADD COLUMN IF NOT EXISTS channel_id BIGINT');
            console.log('✅ Поле channel_id добавлено');

            // Заполняем channel_id значениями из group_id
            const updateResult = await client.query('UPDATE groups SET channel_id = group_id WHERE channel_id IS NULL');
            console.log(`✅ Обновлено записей: ${updateResult.rowCount}`);

            // Добавляем уникальный индекс
            await client.query('CREATE UNIQUE INDEX IF NOT EXISTS idx_groups_channel_id ON groups(channel_id)');
            console.log('✅ Индекс создан');

            await client.query('COMMIT');

            res.json({
                success: true,
                message: 'Миграция channel_id выполнена успешно',
                updatedRows: updateResult.rowCount
            });

        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }

    } catch (error) {
        console.error('Ошибка миграции:', error);
        res.status(500).json({
            error: 'Ошибка миграции',
            message: error.message
        });
    }
});

// Миграция для добавления таблицы игнорируемых пользователей
app.post('/api/migrate/add-ignored-users', async (req, res) => {
    try {
        const client = await pool.connect();

        try {
            await client.query('BEGIN');

            console.log('🔧 Выполняем миграцию ignored_users...');

            // Создаем таблицу игнорируемых пользователей
            await client.query(`
                CREATE TABLE IF NOT EXISTS ignored_users (
                    user_id BIGINT PRIMARY KEY,
                    username VARCHAR(100),
                    display_name VARCHAR(100),
                    added_at TIMESTAMP DEFAULT NOW(),
                    reason TEXT
                )
            `);
            console.log('✅ Таблица ignored_users создана');

            // Добавляем индекс
            await client.query('CREATE INDEX IF NOT EXISTS idx_ignored_users_user_id ON ignored_users(user_id)');
            console.log('✅ Индекс для ignored_users создан');

            await client.query('COMMIT');

            res.json({
                success: true,
                message: 'Миграция ignored_users выполнена успешно'
            });

        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }

    } catch (error) {
        console.error('Ошибка миграции ignored_users:', error);
        res.status(500).json({
            error: 'Ошибка миграции ignored_users',
            message: error.message
        });
    }
});

// Импорт данных из JSON файла (поддержка формата Discord экспорта)
app.post('/api/import/groups', async (req, res) => {
    console.log('🚀 ОБРАБОТЧИК ИМПОРТА ЗАПУЩЕН!');
    try {
        console.log('Получен запрос на импорт данных');
        console.log('Content-Type:', req.headers['content-type']);
        console.log('Body type:', typeof req.body);
        console.log('Body keys:', req.body ? Object.keys(req.body) : 'no body');

        // Поддерживаем два формата: старый {users, groups} и новый {metadata, groups}
        let { groups, users, metadata } = req.body;

        // Если есть metadata, значит это новый формат
        if (metadata && !groups) {
            groups = req.body.groups;
        }

        if (!groups || !Array.isArray(groups)) {
            return res.status(400).json({
                error: 'Неверный формат данных',
                expected: 'Ожидается объект с полем groups (массив)'
            });
        }

        console.log(`Начинаем импорт ${groups.length} групп`);

        const client = await pool.connect();

        try {
            await client.query('BEGIN');

            let importedUsers = 0;
            let importedGroups = 0;
            let importedConnections = 0;
            let processedGroups = 0;

            // Импорт пользователей (если есть отдельный массив)
            if (users && Array.isArray(users)) {
                console.log(`Импортируем ${users.length} пользователей`);
                for (const user of users) {
                    await client.query(`
                        INSERT INTO users (user_id, username, display_name)
                        VALUES ($1, $2, $3)
                        ON CONFLICT (user_id) DO UPDATE SET
                            username = EXCLUDED.username,
                            display_name = EXCLUDED.display_name,
                            last_seen = NOW()
                    `, [user.id, user.username, user.globalName || user.display_name]);
                    importedUsers++;
                }
            }

            // Импорт групп и связей
            for (const group of groups) {
                try {
                    // Добавляем группу
                    await client.query(`
                        INSERT INTO groups (group_id, channel_id, name, description, owner_id, member_count)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (group_id) DO UPDATE SET
                            channel_id = EXCLUDED.channel_id,
                            name = EXCLUDED.name,
                            description = EXCLUDED.description,
                            owner_id = EXCLUDED.owner_id,
                            member_count = EXCLUDED.member_count,
                            updated_at = NOW()
                    `, [
                        group.id,
                        group.id, // channel_id = group_id (это ID канала Discord)
                        group.name || 'Без названия',
                        '', // description пустое
                        group.ownerId,
                        group.participantCount || (group.recipients ? group.recipients.length : 0)
                    ]);
                    importedGroups++;

                    // Добавляем участников группы из recipients
                    if (group.recipients && Array.isArray(group.recipients)) {
                        for (const recipient of group.recipients) {
                            // Сначала добавляем пользователя
                            await client.query(`
                                INSERT INTO users (user_id, username, display_name, avatar_url)
                                VALUES ($1, $2, $3, $4)
                                ON CONFLICT (user_id) DO UPDATE SET
                                    username = EXCLUDED.username,
                                    display_name = EXCLUDED.display_name,
                                    avatar_url = EXCLUDED.avatar_url,
                                    last_seen = NOW()
                            `, [
                                recipient.id,
                                recipient.username,
                                recipient.globalName || recipient.username,
                                recipient.avatar ? `https://cdn.discordapp.com/avatars/${recipient.id}/${recipient.avatar}.png` : null
                            ]);
                            importedUsers++;

                            // Затем добавляем связь пользователь-группа
                            const role = recipient.id === group.ownerId ? 'owner' : 'member';
                            await client.query(`
                                INSERT INTO user_groups (user_id, group_id, role)
                                VALUES ($1, $2, $3)
                                ON CONFLICT (user_id, group_id) DO UPDATE SET
                                    role = EXCLUDED.role,
                                    is_active = TRUE
                            `, [recipient.id, group.id, role]);
                            importedConnections++;
                        }
                    }

                    processedGroups++;

                    // Логируем прогресс каждые 1000 групп
                    if (processedGroups % 1000 === 0) {
                        console.log(`Обработано ${processedGroups}/${groups.length} групп`);
                    }

                } catch (groupError) {
                    console.error(`Ошибка при обработке группы ${group.id}:`, groupError.message);
                    // Продолжаем обработку других групп
                }
            }

            await client.query('COMMIT');
            console.log('Импорт завершен успешно');

            // Очищаем кэш после импорта
            cache.flushAll();

            res.json({
                success: true,
                imported: {
                    users: importedUsers,
                    groups: importedGroups,
                    connections: importedConnections
                },
                metadata: metadata || null,
                processedGroups: processedGroups
            });

        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }

    } catch (error) {
        console.error('Ошибка импорта данных:', error);
        res.status(500).json({
            error: 'Ошибка импорта данных',
            message: error.message,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

// Импорт данных по частям (батчами)
app.post('/api/import/groups/batch', async (req, res) => {
    console.log('🚀 БАТЧЕВЫЙ ИМПОРТ ЗАПУЩЕН!');
    try {
        const { groups, batchNumber = 1, totalBatches = 1, metadata } = req.body;

        if (!groups || !Array.isArray(groups)) {
            return res.status(400).json({
                error: 'Неверный формат данных',
                expected: 'Ожидается объект с полем groups (массив)'
            });
        }

        console.log(`Обрабатываем батч ${batchNumber}/${totalBatches} с ${groups.length} группами`);

        const client = await pool.connect();

        try {
            await client.query('BEGIN');

            const result = await processBatch(client, groups);

            await client.query('COMMIT');
            console.log(`Батч ${batchNumber} обработан успешно`);

            // Очищаем кэш только после последнего батча
            if (batchNumber === totalBatches) {
                cache.flushAll();
                console.log('Все батчи обработаны, кэш очищен');
            }

            res.json({
                success: true,
                batchNumber: batchNumber,
                totalBatches: totalBatches,
                imported: {
                    users: result.users,
                    groups: result.groups,
                    connections: result.connections
                },
                metadata: batchNumber === 1 ? metadata : null,
                processedGroups: groups.length
            });

        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }

    } catch (error) {
        console.error('Ошибка батчевого импорта:', error);
        res.status(500).json({
            error: 'Ошибка батчевого импорта',
            message: error.message
        });
    }
});

// Потоковый импорт больших файлов через загрузку файла (ОТКЛЮЧЕН)
const upload = multer({
    dest: '/tmp/',
    limits: { fileSize: 1000 * 1024 * 1024 } // 1GB
});

app.post('/api/import/groups/stream', upload.single('file'), async (req, res) => {
    console.log('🚀 ПОТОКОВЫЙ ИМПОРТ ЗАПУЩЕН!');

    if (!req.file) {
        return res.status(400).json({ error: 'Файл не загружен' });
    }

    console.log('Загружен файл:', req.file.originalname, 'Размер:', req.file.size);

    try {
        const fs = require('fs');
        const path = require('path');

        // Читаем файл как поток
        const fileStream = fs.createReadStream(req.file.path);

        let metadata = null;
        let groupsProcessed = 0;
        let usersImported = 0;
        let groupsImported = 0;
        let connectionsImported = 0;
        let batchSize = 100; // Обрабатываем по 100 групп за раз
        let currentBatch = [];

        const client = await pool.connect();

        try {
            await client.query('BEGIN');

            // Создаем поток для парсинга JSON
            const pipeline = fileStream
                .pipe(parser())
                .pipe(StreamValues.withParser());

            let isInGroups = false;
            let currentGroup = null;
            let groupBuffer = '';
            let bracketCount = 0;

            for await (const data of pipeline) {
                const { key, value } = data;

                // Получаем metadata
                if (key === 'metadata') {
                    metadata = value;
                    console.log('Получена metadata:', metadata);
                    continue;
                }

                // Обрабатываем группы
                if (key === 'groups') {
                    console.log('Начинаем обработку групп...');
                    continue;
                }

                // Если мы в массиве групп
                if (typeof key === 'number' && value && typeof value === 'object') {
                    currentBatch.push(value);

                    // Обрабатываем батч когда он заполнится
                    if (currentBatch.length >= batchSize) {
                        const result = await processBatch(client, currentBatch);
                        usersImported += result.users;
                        groupsImported += result.groups;
                        connectionsImported += result.connections;
                        groupsProcessed += currentBatch.length;

                        console.log(`Обработано ${groupsProcessed} групп...`);
                        currentBatch = [];
                    }
                }
            }

            // Обрабатываем оставшиеся группы
            if (currentBatch.length > 0) {
                const result = await processBatch(client, currentBatch);
                usersImported += result.users;
                groupsImported += result.groups;
                connectionsImported += result.connections;
                groupsProcessed += currentBatch.length;
            }

            await client.query('COMMIT');
            console.log('Потоковый импорт завершен успешно');

            // Очищаем кэш
            cache.flushAll();

            // Удаляем временный файл
            fs.unlinkSync(req.file.path);

            res.json({
                success: true,
                imported: {
                    users: usersImported,
                    groups: groupsImported,
                    connections: connectionsImported
                },
                metadata: metadata,
                processedGroups: groupsProcessed
            });

        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
            // Удаляем временный файл в случае ошибки
            try {
                fs.unlinkSync(req.file.path);
            } catch (e) {
                // Игнорируем ошибки удаления
            }
        }

    } catch (error) {
        console.error('Ошибка потокового импорта:', error);
        res.status(500).json({
            error: 'Ошибка потокового импорта',
            message: error.message
        });
    }
});

// Функция для обработки батча групп
async function processBatch(client, groups) {
    let usersCount = 0;
    let groupsCount = 0;
    let connectionsCount = 0;

    for (const group of groups) {
        try {
            // Добавляем группу
            await client.query(`
                INSERT INTO groups (group_id, channel_id, name, description, owner_id, member_count)
                VALUES ($1, $2, $3, $4, $5, $6)
                ON CONFLICT (group_id) DO UPDATE SET
                    channel_id = EXCLUDED.channel_id,
                    name = EXCLUDED.name,
                    description = EXCLUDED.description,
                    owner_id = EXCLUDED.owner_id,
                    member_count = EXCLUDED.member_count,
                    updated_at = NOW()
            `, [
                group.id,
                group.id, // channel_id = group_id (это ID канала Discord)
                group.name || 'Без названия',
                '',
                group.ownerId,
                group.participantCount || (group.recipients ? group.recipients.length : 0)
            ]);
            groupsCount++;

            // Добавляем участников группы
            if (group.recipients && Array.isArray(group.recipients)) {
                for (const recipient of group.recipients) {
                    // Добавляем пользователя
                    await client.query(`
                        INSERT INTO users (user_id, username, display_name, avatar_url)
                        VALUES ($1, $2, $3, $4)
                        ON CONFLICT (user_id) DO UPDATE SET
                            username = EXCLUDED.username,
                            display_name = EXCLUDED.display_name,
                            avatar_url = EXCLUDED.avatar_url,
                            last_seen = NOW()
                    `, [
                        recipient.id,
                        recipient.username,
                        recipient.globalName || recipient.username,
                        recipient.avatar ? `https://cdn.discordapp.com/avatars/${recipient.id}/${recipient.avatar}.png` : null
                    ]);
                    usersCount++;

                    // Добавляем связь пользователь-группа
                    const role = recipient.id === group.ownerId ? 'owner' : 'member';
                    await client.query(`
                        INSERT INTO user_groups (user_id, group_id, role)
                        VALUES ($1, $2, $3)
                        ON CONFLICT (user_id, group_id) DO UPDATE SET
                            role = EXCLUDED.role,
                            is_active = TRUE
                    `, [recipient.id, group.id, role]);
                    connectionsCount++;
                }
            }

        } catch (groupError) {
            console.error(`Ошибка при обработке группы ${group.id}:`, groupError.message);
        }
    }

    return { users: usersCount, groups: groupsCount, connections: connectionsCount };
}

// Middleware для проверки авторизации админа
function requireAuth(req, res, next) {
    if (req.session && req.session.isAdmin) {
        return next();
    } else {
        return res.status(401).json({ error: 'Требуется авторизация' });
    }
}

// Главная страница
app.get('/', (req, res) => {
    res.json({
        message: 'Discord Groups API',
        version: '1.0.0',
        endpoints: {
            'GET /': 'Эта страница',
            'GET /upload': 'Страница загрузки файлов',
            'GET /admin': 'Админ-панель для управления игнорируемыми пользователями',
            'POST /api/migrate/add-ignored-users': 'Миграция: создать таблицу игнорируемых пользователей',
            'GET /api/user/:userId/groups': 'Получить группы пользователя',
            'GET /api/group/:groupId/users': 'Получить пользователей группы',
            'GET /api/search/groups': 'Поиск групп по названию',
            'POST /api/import/groups': 'Импорт данных групп (JSON)',
            'POST /api/import/groups/batch': 'Импорт данных по частям (батчами)',
            'POST /api/import/groups/stream': 'Импорт больших файлов (потоковый, экспериментальный)',
            'GET /api/stats': 'Статистика базы данных',
            'GET /api/ignored-users': 'Получить список игнорируемых пользователей (требует авторизации)',
            'POST /api/ignored-users': 'Добавить пользователя в игнорируемые (требует авторизации)',
            'DELETE /api/ignored-users/:userId': 'Удалить пользователя из игнорируемых (требует авторизации)',
            'GET /api/ignored-users/list': 'Получить список ID игнорируемых пользователей (для плагина)'
        }
    });
});

// Страница загрузки файлов
app.get('/upload', (req, res) => {
    const fs = require('fs');
    const path = require('path');
    const htmlPath = path.join(__dirname, '..', 'upload.html');

    if (fs.existsSync(htmlPath)) {
        res.sendFile(htmlPath);
    } else {
        res.status(404).send('Страница загрузки не найдена');
    }
});

// JavaScript файл для страницы загрузки
app.get('/upload.js', (req, res) => {
    const fs = require('fs');
    const path = require('path');
    const jsPath = path.join(__dirname, '..', 'upload.js');

    if (fs.existsSync(jsPath)) {
        res.setHeader('Content-Type', 'application/javascript');
        res.sendFile(jsPath);
    } else {
        res.status(404).send('JavaScript файл не найден');
    }
});

// Получить группы пользователя
app.get('/api/user/:userId/groups', async (req, res) => {
    try {
        let { userId } = req.params;
        const { limit = 500, offset = 0, search = '' } = req.query;

        // Проверяем, является ли userId числом (Discord ID) или строкой (username)
        let actualUserId = userId;
        if (!/^\d{17,19}$/.test(userId)) {
            // Если это не Discord ID, ищем пользователя по username
            const userResult = await pool.query(
                'SELECT user_id FROM users WHERE username = $1',
                [userId]
            );

            if (userResult.rows.length === 0) {
                return res.status(404).json({
                    error: 'Пользователь не найден',
                    message: `Пользователь с ID/username "${userId}" не найден`
                });
            }

            actualUserId = userResult.rows[0].user_id;
        }

        // Проверяем кэш
        const cacheKey = `user_groups_${actualUserId}_${limit}_${offset}_${search}`;
        const cached = cache.get(cacheKey);
        if (cached) {
            return res.json({ ...cached, cached: true });
        }

        // Используем функцию PostgreSQL для быстрого поиска
        const result = await pool.query(
            'SELECT * FROM get_user_groups($1, $2, $3, $4)',
            [actualUserId, parseInt(limit), parseInt(offset), search || null]
        );

        // Получаем общее количество групп пользователя
        const countResult = await pool.query(
            'SELECT COUNT(*) FROM user_groups WHERE user_id = $1 AND is_active = TRUE',
            [actualUserId]
        );

        const response = {
            groups: result.rows,
            total: parseInt(countResult.rows[0].count),
            limit: parseInt(limit),
            offset: parseInt(offset),
            hasMore: result.rows.length === parseInt(limit)
        };

        // Сохраняем в кэш
        cache.set(cacheKey, response);

        res.json(response);
    } catch (error) {
        console.error('Ошибка получения групп пользователя:', error);
        res.status(500).json({ 
            error: 'Внутренняя ошибка сервера',
            message: error.message 
        });
    }
});

// Получить пользователей группы
app.get('/api/group/:groupId/users', async (req, res) => {
    try {
        const { groupId } = req.params;
        const { limit = 500, offset = 0 } = req.query;

        const cacheKey = `group_users_${groupId}_${limit}_${offset}`;
        const cached = cache.get(cacheKey);
        if (cached) {
            return res.json({ ...cached, cached: true });
        }

        const result = await pool.query(`
            SELECT 
                u.user_id,
                u.username,
                u.display_name,
                ug.joined_at,
                ug.role
            FROM user_groups ug
            JOIN users u ON ug.user_id = u.user_id
            WHERE ug.group_id = $1 AND ug.is_active = TRUE
            ORDER BY ug.joined_at ASC
            LIMIT $2 OFFSET $3
        `, [groupId, limit, offset]);

        const countResult = await pool.query(
            'SELECT member_count FROM groups WHERE group_id = $1',
            [groupId]
        );

        const response = {
            users: result.rows,
            total: countResult.rows[0]?.member_count || 0,
            limit: parseInt(limit),
            offset: parseInt(offset),
            hasMore: result.rows.length === parseInt(limit)
        };

        cache.set(cacheKey, response);
        res.json(response);
    } catch (error) {
        console.error('Ошибка получения пользователей группы:', error);
        res.status(500).json({ 
            error: 'Внутренняя ошибка сервера',
            message: error.message 
        });
    }
});

// Поиск групп по названию
app.get('/api/search/groups', async (req, res) => {
    try {
        const { q: query, limit = 100, offset = 0 } = req.query;
        
        if (!query || query.length < 2) {
            return res.status(400).json({ 
                error: 'Запрос должен содержать минимум 2 символа' 
            });
        }

        const cacheKey = `search_groups_${query}_${limit}_${offset}`;
        const cached = cache.get(cacheKey);
        if (cached) {
            return res.json({ ...cached, cached: true });
        }

        const result = await pool.query(`
            SELECT 
                group_id,
                name,
                description,
                member_count,
                created_at
            FROM groups
            WHERE name ILIKE $1 OR description ILIKE $1
            ORDER BY member_count DESC, name ASC
            LIMIT $2 OFFSET $3
        `, [`%${query}%`, limit, offset]);

        const response = {
            groups: result.rows,
            query,
            total: result.rows.length,
            limit: parseInt(limit),
            offset: parseInt(offset)
        };

        cache.set(cacheKey, response);
        res.json(response);
    } catch (error) {
        console.error('Ошибка поиска групп:', error);
        res.status(500).json({ 
            error: 'Внутренняя ошибка сервера',
            message: error.message 
        });
    }
});

// Статистика базы данных
app.get('/api/stats', async (req, res) => {
    try {
        const cacheKey = 'db_stats';
        const cached = cache.get(cacheKey);
        if (cached) {
            return res.json({ ...cached, cached: true });
        }

        const [usersResult, groupsResult, connectionsResult] = await Promise.all([
            pool.query('SELECT COUNT(*) as count FROM users'),
            pool.query('SELECT COUNT(*) as count FROM groups'),
            pool.query('SELECT COUNT(*) as count FROM user_groups WHERE is_active = TRUE')
        ]);

        const response = {
            users: parseInt(usersResult.rows[0].count),
            groups: parseInt(groupsResult.rows[0].count),
            connections: parseInt(connectionsResult.rows[0].count),
            timestamp: new Date().toISOString()
        };

        cache.set(cacheKey, response, 60); // Кэш на 1 минуту
        res.json(response);
    } catch (error) {
        console.error('Ошибка получения статистики:', error);
        res.status(500).json({
            error: 'Внутренняя ошибка сервера',
            message: error.message
        });
    }
});

// === АДМИН-ПАНЕЛЬ ===

// Админ-страница
app.get('/admin', (req, res) => {
    const fs = require('fs');
    const path = require('path');
    const htmlPath = path.join(__dirname, '..', 'admin.html');

    console.log('Ищем admin.html по пути:', htmlPath);
    console.log('Файл существует:', fs.existsSync(htmlPath));

    if (fs.existsSync(htmlPath)) {
        res.sendFile(path.resolve(htmlPath));
    } else {
        // Попробуем альтернативные пути
        const altPaths = [
            path.join(__dirname, 'admin.html'),
            path.join(process.cwd(), 'admin.html'),
            './admin.html'
        ];

        let found = false;
        for (const altPath of altPaths) {
            console.log('Проверяем альтернативный путь:', altPath);
            if (fs.existsSync(altPath)) {
                console.log('Найден файл по пути:', altPath);
                res.sendFile(path.resolve(altPath));
                found = true;
                break;
            }
        }

        if (!found) {
            res.status(404).send(`Админ-страница не найдена. Проверенные пути: ${htmlPath}, ${altPaths.join(', ')}`);
        }
    }
});

// Авторизация админа
app.post('/admin/login', (req, res) => {
    const { password } = req.body;

    if (password === ADMIN_PASSWORD) {
        req.session.isAdmin = true;
        res.json({ success: true, message: 'Авторизация успешна' });
    } else {
        res.status(401).json({ error: 'Неверный пароль' });
    }
});

// Выход из админ-панели
app.post('/admin/logout', (req, res) => {
    req.session.destroy();
    res.json({ success: true, message: 'Выход выполнен' });
});

// Проверка авторизации
app.get('/admin/check', (req, res) => {
    res.json({ isAdmin: !!(req.session && req.session.isAdmin) });
});

// Получить список игнорируемых пользователей
app.get('/api/ignored-users', requireAuth, async (req, res) => {
    try {
        const result = await pool.query(`
            SELECT user_id, username, display_name, added_at, reason
            FROM ignored_users
            ORDER BY added_at DESC
        `);

        res.json({
            success: true,
            users: result.rows
        });
    } catch (error) {
        console.error('Ошибка получения игнорируемых пользователей:', error);
        res.status(500).json({
            error: 'Внутренняя ошибка сервера',
            message: error.message
        });
    }
});

// Добавить пользователя в игнорируемые
app.post('/api/ignored-users', requireAuth, async (req, res) => {
    try {
        const { userId, username, displayName, reason } = req.body;

        if (!userId) {
            return res.status(400).json({ error: 'Требуется ID пользователя' });
        }

        await pool.query(`
            INSERT INTO ignored_users (user_id, username, display_name, reason)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (user_id) DO UPDATE SET
                username = EXCLUDED.username,
                display_name = EXCLUDED.display_name,
                reason = EXCLUDED.reason,
                added_at = NOW()
        `, [userId, username, displayName, reason]);

        res.json({ success: true, message: 'Пользователь добавлен в игнорируемые' });
    } catch (error) {
        console.error('Ошибка добавления игнорируемого пользователя:', error);
        res.status(500).json({
            error: 'Внутренняя ошибка сервера',
            message: error.message
        });
    }
});

// Удалить пользователя из игнорируемых
app.delete('/api/ignored-users/:userId', requireAuth, async (req, res) => {
    try {
        const { userId } = req.params;

        const result = await pool.query(
            'DELETE FROM ignored_users WHERE user_id = $1',
            [userId]
        );

        if (result.rowCount === 0) {
            return res.status(404).json({ error: 'Пользователь не найден в игнорируемых' });
        }

        res.json({ success: true, message: 'Пользователь удален из игнорируемых' });
    } catch (error) {
        console.error('Ошибка удаления игнорируемого пользователя:', error);
        res.status(500).json({
            error: 'Внутренняя ошибка сервера',
            message: error.message
        });
    }
});

// Получить список игнорируемых пользователей (публичный эндпойнт для плагина)
app.get('/api/ignored-users/list', async (req, res) => {
    try {
        const result = await pool.query('SELECT user_id FROM ignored_users');
        const ignoredUserIds = result.rows.map(row => row.user_id.toString());

        res.json({
            success: true,
            ignoredUsers: ignoredUserIds
        });
    } catch (error) {
        console.error('Ошибка получения списка игнорируемых пользователей:', error);
        res.status(500).json({
            error: 'Внутренняя ошибка сервера',
            message: error.message
        });
    }
});



// Тестовый эндпоинт
app.get('/api/test', (req, res) => {
    console.log('🔍 Тестовый эндпоинт работает!');
    res.json({ message: 'Тест успешен!' });
});



// Тестовый GET эндпоинт для проверки
app.get('/api/import/groups', (req, res) => {
    console.log('🔍 GET запрос к /api/import/groups');
    res.json({ message: 'GET эндпоинт работает!' });
});

// Импорт данных из JSON файла (поддержка формата Discord экспорта)
app.post('/api/import/groups', async (req, res) => {
    console.log('🚀 ОБРАБОТЧИК ИМПОРТА ЗАПУЩЕН!');
    try {
        console.log('Получен запрос на импорт данных');
        console.log('Content-Type:', req.headers['content-type']);
        console.log('Body type:', typeof req.body);
        console.log('Body keys:', req.body ? Object.keys(req.body) : 'no body');

        // Поддерживаем два формата: старый {users, groups} и новый {metadata, groups}
        let { groups, users, metadata } = req.body;

        // Если есть metadata, значит это новый формат
        if (metadata && !groups) {
            groups = req.body.groups;
        }

        if (!groups || !Array.isArray(groups)) {
            return res.status(400).json({
                error: 'Неверный формат данных',
                expected: 'Ожидается объект с полем groups (массив)'
            });
        }

        console.log(`Начинаем импорт ${groups.length} групп`);

        const client = await pool.connect();

        try {
            await client.query('BEGIN');

            let importedUsers = 0;
            let importedGroups = 0;
            let importedConnections = 0;
            let processedGroups = 0;

            // Импорт пользователей (если есть отдельный массив)
            if (users && Array.isArray(users)) {
                console.log(`Импортируем ${users.length} пользователей`);
                for (const user of users) {
                    await client.query(`
                        INSERT INTO users (user_id, username, display_name)
                        VALUES ($1, $2, $3)
                        ON CONFLICT (user_id) DO UPDATE SET
                            username = EXCLUDED.username,
                            display_name = EXCLUDED.display_name,
                            last_seen = NOW()
                    `, [user.id, user.username, user.globalName || user.display_name]);
                    importedUsers++;
                }
            }

            // Импорт групп и связей
            for (const group of groups) {
                try {
                    // Добавляем группу
                    await client.query(`
                        INSERT INTO groups (group_id, name, description, owner_id, member_count)
                        VALUES ($1, $2, $3, $4, $5)
                        ON CONFLICT (group_id) DO UPDATE SET
                            name = EXCLUDED.name,
                            description = EXCLUDED.description,
                            owner_id = EXCLUDED.owner_id,
                            member_count = EXCLUDED.member_count,
                            updated_at = NOW()
                    `, [
                        group.id,
                        group.name || 'Без названия',
                        '', // description пустое
                        group.ownerId,
                        group.participantCount || (group.recipients ? group.recipients.length : 0)
                    ]);
                    importedGroups++;

                    // Добавляем участников группы из recipients
                    if (group.recipients && Array.isArray(group.recipients)) {
                        for (const recipient of group.recipients) {
                            // Сначала добавляем пользователя
                            await client.query(`
                                INSERT INTO users (user_id, username, display_name, avatar_url)
                                VALUES ($1, $2, $3, $4)
                                ON CONFLICT (user_id) DO UPDATE SET
                                    username = EXCLUDED.username,
                                    display_name = EXCLUDED.display_name,
                                    avatar_url = EXCLUDED.avatar_url,
                                    last_seen = NOW()
                            `, [
                                recipient.id,
                                recipient.username,
                                recipient.globalName || recipient.username,
                                recipient.avatar ? `https://cdn.discordapp.com/avatars/${recipient.id}/${recipient.avatar}.png` : null
                            ]);
                            importedUsers++;

                            // Затем добавляем связь пользователь-группа
                            const role = recipient.id === group.ownerId ? 'owner' : 'member';
                            await client.query(`
                                INSERT INTO user_groups (user_id, group_id, role)
                                VALUES ($1, $2, $3)
                                ON CONFLICT (user_id, group_id) DO UPDATE SET
                                    role = EXCLUDED.role,
                                    is_active = TRUE
                            `, [recipient.id, group.id, role]);
                            importedConnections++;
                        }
                    }

                    processedGroups++;

                    // Логируем прогресс каждые 1000 групп
                    if (processedGroups % 1000 === 0) {
                        console.log(`Обработано ${processedGroups}/${groups.length} групп`);
                    }

                } catch (groupError) {
                    console.error(`Ошибка при обработке группы ${group.id}:`, groupError.message);
                    // Продолжаем обработку других групп
                }
            }

            await client.query('COMMIT');
            console.log('Импорт завершен успешно');

            // Очищаем кэш после импорта
            cache.flushAll();

            res.json({
                success: true,
                imported: {
                    users: importedUsers,
                    groups: importedGroups,
                    connections: importedConnections
                },
                metadata: metadata || null,
                processedGroups: processedGroups
            });

        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }

    } catch (error) {
        console.error('Ошибка импорта данных:', error);
        res.status(500).json({
            error: 'Ошибка импорта данных',
            message: error.message,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

// Обработчик для неизвестных маршрутов
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Эндпоинт не найден',
        path: req.originalUrl
    });
});

// Глобальный обработчик ошибок
app.use((error, req, res, next) => {
    console.error('Глобальная ошибка:', error);
    res.status(500).json({
        error: 'Внутренняя ошибка сервера',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Что-то пошло не так'
    });
});

// Запуск сервера
app.listen(PORT, () => {
    console.log(`🚀 Discord Groups API запущен на порту ${PORT}`);
    console.log(`📊 Статистика: http://localhost:${PORT}/api/stats`);
    console.log(`🔍 Документация: http://localhost:${PORT}/`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('Получен сигнал SIGTERM, завершаем работу...');
    pool.end(() => {
        console.log('Подключение к БД закрыто');
        process.exit(0);
    });
});
