/**
 * @name AutoGroupMonitor
 * <AUTHOR>
 * @authorId YourDiscordId
 * @version 1.0.0
 * @description Автоматический мониторинг групп Discord для обновления базы данных 24/7
 * @website https://github.com/your-repo
 * @source https://github.com/your-repo/AutoGroupMonitor
 */

module.exports = (() => {
    const config = {
        info: {
            name: "AutoGroupMonitor",
            authors: [{
                name: "<PERSON><PERSON><PERSON>",
                discord_id: "YourDiscordId",
                github_username: "<PERSON><PERSON><PERSON><PERSON>"
            }],
            version: "1.0.0",
            description: "Автоматический мониторинг групп Discord для обновления базы данных 24/7",
            github: "https://github.com/your-repo/AutoGroupMonitor",
            github_raw: "https://raw.githubusercontent.com/your-repo/AutoGroupMonitor/main/AutoGroupMonitor.plugin.js"
        },
        changelog: [
            {
                title: "v1.0.0",
                items: [
                    "Начальная версия",
                    "Мониторинг создания новых групп",
                    "Автоматическое добавление пользователей в БД",
                    "API интеграция с сервером"
                ]
            }
        ],
        defaultConfig: [
            {
                type: "textbox",
                id: "apiUrl",
                name: "API URL",
                note: "URL вашего API сервера",
                value: "http://localhost:8000"
            },
            {
                type: "switch",
                id: "enableLogging",
                name: "Включить логирование",
                note: "Показывать подробные логи в консоли",
                value: true
            },
            {
                type: "switch",
                id: "realTimeMonitoring",
                name: "Мониторинг в реальном времени",
                note: "Отслеживать создание групп и добавление пользователей мгновенно",
                value: true
            },

            {
                type: "slider",
                id: "retryAttempts",
                name: "Попытки повтора при ошибке",
                note: "Сколько раз повторять запрос при ошибке API",
                value: 3,
                min: 1,
                max: 10,
                units: "раз"
            },
            {
                type: "slider",
                id: "requestTimeout",
                name: "Таймаут запроса (секунды)",
                note: "Максимальное время ожидания ответа от API",
                value: 30,
                min: 5,
                max: 120,
                units: "сек"
            }
        ]
    };

    return !global.ZeresPluginLibrary ? class {
        constructor() { this._config = config; }
        getName() { return config.info.name; }
        getAuthor() { return config.info.authors.map(a => a.name).join(", "); }
        getDescription() { return config.info.description; }
        getVersion() { return config.info.version; }
        load() {
            BdApi.showConfirmationModal("Library Missing", `The library plugin needed for ${config.info.name} is missing. Please click Download Now to install it.`, {
                confirmText: "Download Now",
                cancelText: "Cancel",
                onConfirm: () => {
                    require("request").get("https://rauenzi.github.io/BDPluginLibrary/release/0PluginLibrary.plugin.js", async (error, response, body) => {
                        if (error) return require("electron").shell.openExternal("https://betterdiscord.app/Download?id=9");
                        await new Promise(r => require("fs").writeFile(require("path").join(BdApi.Plugins.folder, "0PluginLibrary.plugin.js"), body, r));
                    });
                }
            });
        }
        start() { }
        stop() { }
    } : (([Plugin, Api]) => {
        const plugin = (Plugin, Library) => {
            const { DiscordModules, WebpackModules, Patcher, Logger, Settings, Utilities } = Library;
            const { ChannelStore, UserStore } = DiscordModules;

            return class AutoGroupMonitor extends Plugin {
                constructor() {
                    super();
                    this.knownGroups = new Set();
                    this.isInitialized = false;
                    this.retryQueue = [];
                    this.retryQueueInterval = null;
                }

                getDefaultSettings() {
                    return {
                        apiUrl: "http://localhost:8000",
                        enableLogging: true,
                        realTimeMonitoring: true,
                        retryAttempts: 3,
                        requestTimeout: 30
                    };
                }

                loadSettings() {
                    return Utilities.loadSettings(this.getName(), this.getDefaultSettings());
                }

                saveSettings() {
                    Utilities.saveSettings(this.getName(), this.settings);
                }

                onStart() {
                    this.settings = this.loadSettings();
                    this.log("🚀 AutoGroupMonitor запущен!");
                    this.initializeMonitoring();
                    this.patchChannelEvents();
                }

                onStop() {
                    this.log("🛑 AutoGroupMonitor остановлен!");

                    // Очищаем интервал очереди повтора
                    if (this.retryQueueInterval) {
                        clearInterval(this.retryQueueInterval);
                        this.retryQueueInterval = null;
                    }



                    // Очищаем очередь повтора
                    if (this.retryQueue) {
                        this.log(`📝 Очищаем очередь повтора (${this.retryQueue.length} элементов)`);
                        this.retryQueue = [];
                    }

                    // Убираем все патчи
                    Patcher.unpatchAll();

                    this.isInitialized = false;
                }

                patchChannelEvents() {
                    if (!this.settings.realTimeMonitoring) {
                        this.log("⏸️ Мониторинг в реальном времени отключен");
                        return;
                    }

                    // Патчим Dispatcher для отслеживания событий каналов в реальном времени
                    const Dispatcher = WebpackModules.getByProps("dispatch", "subscribe");

                    if (Dispatcher) {
                        Patcher.before(Dispatcher, "dispatch", (_, args) => {
                            if (!this.settings.realTimeMonitoring) return; // Дополнительная проверка

                            const [event] = args;



                            // Отслеживаем создание новых каналов
                            if (event.type === "CHANNEL_CREATE" && event.channel?.type === 3) {
                                this.log(`🔔 Событие CHANNEL_CREATE: новая группа ${event.channel.id}`);
                                setTimeout(() => this.handleNewChannel(event.channel), 1000);
                            }

                            // Отслеживаем добавление участников в группы
                            if (event.type === "CHANNEL_RECIPIENT_ADD") {
                                this.log(`👥 Событие CHANNEL_RECIPIENT_ADD: пользователь ${event.user?.username || event.user?.id} добавлен в канал ${event.channelId}`);
                                const channel = ChannelStore.getChannel(event.channelId);
                                if (channel && channel.type === 3) {
                                    setTimeout(() => this.handleChannelUpdate(channel), 2000); // Увеличили задержку
                                }
                            }

                            // Дополнительно отслеживаем обновления каналов
                            if (event.type === "CHANNEL_UPDATE" && event.channel?.type === 3) {
                                // Проверяем, что именно изменилось
                                const oldChannel = ChannelStore.getChannel(event.channel.id);
                                const newChannel = event.channel;

                                let changeType = "неизвестное изменение";
                                if (oldChannel && oldChannel.name !== newChannel.name) {
                                    changeType = `название: "${oldChannel.name || 'Без названия'}" → "${newChannel.name || 'Без названия'}"`;
                                } else if (oldChannel && oldChannel.icon !== newChannel.icon) {
                                    changeType = "иконка группы";
                                } else if (oldChannel && (oldChannel.recipients?.length || 0) !== (newChannel.recipients?.length || 0)) {
                                    changeType = `состав участников: ${oldChannel.recipients?.length || 0} → ${newChannel.recipients?.length || 0}`;
                                }

                                this.log(`🔄 Событие CHANNEL_UPDATE: ${changeType} в группе ${event.channel.id}`);
                                setTimeout(() => this.handleChannelUpdate(event.channel), 1500);
                            }

                            // Отслеживаем события получателей
                            if (event.type === "CHANNEL_RECIPIENT_REMOVE") {
                                this.log(`👤 Событие CHANNEL_RECIPIENT_REMOVE: пользователь ${event.user?.username || event.user?.id} удален из канала ${event.channelId}`);
                                // Не обрабатываем удаление, как требовалось
                            }

                            // Отслеживаем события изменения названия группы
                            if (event.type === "CHANNEL_UPDATES" && event.channels) {
                                for (const updatedChannel of event.channels) {
                                    if (updatedChannel.type === 3) {
                                        this.log(`📝 Событие CHANNEL_UPDATES: обновление группы ${updatedChannel.id}`);
                                        setTimeout(() => this.handleChannelUpdate(updatedChannel), 1500);
                                    }
                                }
                            }

                            // Отслеживаем все события связанные с каналами для отладки
                            if (event.type && (
                                event.type.includes("RECIPIENT") ||
                                event.type.includes("GROUP") ||
                                (event.type.includes("CHANNEL") && event.channelId && ChannelStore.getChannel(event.channelId)?.type === 3)
                            )) {
                                this.log(`� Групповое событие: ${event.type}`, {
                                    channelId: event.channelId,
                                    userId: event.user?.id,
                                    username: event.user?.username
                                });
                            }
                        });

                        this.log("✅ Патчи для отслеживания событий установлены");
                    } else {
                        this.error("❌ Не удалось найти Dispatcher для патчинга");
                    }
                }

                async handleNewChannel(channel) {
                    try {
                        if (!this.knownGroups.has(channel.id)) {
                            this.log(`🆕 Обработка новой группы из события: ${channel.name || 'Без названия'} (${channel.id})`);
                            this.knownGroups.add(channel.id);

                            // Небольшая задержка для загрузки всех данных канала
                            setTimeout(async () => {
                                const fullChannel = ChannelStore.getChannel(channel.id);
                                if (fullChannel) {
                                    // Сохраняем начальные данные новой группы
                                    const groupKey = `group_${channel.id}_lastData`;
                                    this[groupKey] = {
                                        name: fullChannel.name || null,
                                        participantCount: (fullChannel.recipients?.length || 0) + 1,
                                        lastUpdated: Date.now()
                                    };

                                    await this.processGroup(fullChannel, true);
                                }
                            }, 2000);
                        }
                    } catch (error) {
                        this.error("❌ Ошибка обработки нового канала:", error);
                    }
                }

                async handleChannelUpdate(channel) {
                    try {
                        this.log(`🔄 Обработка обновления группы: ${channel.name || 'Без названия'} (${channel.id})`);

                        // Небольшая задержка для загрузки обновленных данных
                        setTimeout(async () => {
                            const fullChannel = ChannelStore.getChannel(channel.id);
                            if (fullChannel) {
                                // Проверяем, сохранили ли мы предыдущее название этой группы
                                const groupKey = `group_${channel.id}_lastData`;
                                const lastData = this[groupKey] || {};

                                const currentName = fullChannel.name || null;
                                const lastKnownName = lastData.name || null;

                                // Если название изменилось, логируем это
                                if (lastKnownName !== null && lastKnownName !== currentName) {
                                    this.log(`📝 Изменение названия группы ${channel.id}: "${lastKnownName}" → "${currentName || 'Без названия'}"`);
                                }

                                // Сохраняем текущие данные для будущих сравнений
                                this[groupKey] = {
                                    name: currentName,
                                    participantCount: (fullChannel.recipients?.length || 0) + 1, // +1 для текущего пользователя
                                    lastUpdated: Date.now()
                                };

                                await this.processGroup(fullChannel, false);
                            }
                        }, 1000);
                    } catch (error) {
                        this.error("❌ Ошибка обработки обновления канала:", error);
                    }
                }

                log(message, ...args) {
                    if (this.settings.enableLogging) {
                        Logger.info(this.getName(), message, ...args);
                    }
                }

                error(message, ...args) {
                    Logger.error(this.getName(), message, ...args);
                }

                async initializeMonitoring() {
                    try {
                        // Инициализируем только Set для отслеживания известных групп
                        const channels = ChannelStore.getSortedPrivateChannels();
                        const groupChannels = channels.filter(channel => channel.type === 3);

                        // Запоминаем ID всех существующих групп и их базовые данные для отслеживания изменений
                        for (const channel of groupChannels) {
                            this.knownGroups.add(channel.id);

                            // Сохраняем начальные данные группы для отслеживания изменений
                            const groupKey = `group_${channel.id}_lastData`;
                            this[groupKey] = {
                                name: channel.name || null,
                                participantCount: (channel.recipients?.length || 0) + 1, // +1 для текущего пользователя
                                lastUpdated: Date.now()
                            };
                        }

                        this.log(`📊 Инициализировано отслеживание ${groupChannels.length} существующих групп`);
                        this.log("🔄 Используется мониторинг событий в реальном времени");



                        this.isInitialized = true;
                        this.log("✅ Мониторинг инициализирован успешно");
                    } catch (error) {
                        this.error("❌ Ошибка инициализации мониторинга:", error);
                    }
                }





                async processGroup(channel, isNewGroup = false) {
                    try {
                        const groupData = await this.extractGroupData(channel);
                        
                        if (isNewGroup) {
                            this.log(`📝 Обрабатываем новую группу: ${groupData.name}`);
                        }
                        
                        await this.sendToAPI(groupData, isNewGroup);
                    } catch (error) {
                        this.error(`❌ Ошибка обработки группы ${channel.id}:`, error);
                    }
                }

                async extractGroupData(channel) {
                    const recipients = channel.recipients || [];
                    const currentUser = UserStore.getCurrentUser();
                    
                    // Добавляем текущего пользователя к участникам
                    const allParticipants = [currentUser.id, ...recipients];
                    
                    const participantsData = [];
                    
                    for (const userId of allParticipants) {
                        const user = UserStore.getUser(userId);
                        if (user) {
                            participantsData.push({
                                id: user.id,
                                username: user.username,
                                globalName: user.globalName || user.displayName,
                                discriminator: user.discriminator || "0",
                                avatar: user.avatar,
                                bot: user.bot || false,
                                system: user.system || false,
                                available: true
                            });
                        }
                    }
                    
                    return {
                        id: channel.id,
                        name: channel.name || `Группа ${participantsData.length} участников`,
                        type: "GROUP_DM",
                        ownerId: channel.ownerId || currentUser.id,
                        recipients: participantsData,
                        participantCount: participantsData.length,
                        createdAt: this.snowflakeToTimestamp(channel.id),
                        lastMessageId: channel.lastMessageId,
                        lastMessageTimestamp: channel.lastMessageId ? this.snowflakeToTimestamp(channel.lastMessageId) : null,
                        icon: channel.icon,
                        parsedAt: new Date().toISOString()
                    };
                }

                snowflakeToTimestamp(snowflake) {
                    try {
                        const timestamp = (BigInt(snowflake) >> 22n) + 1420070400000n;
                        return new Date(Number(timestamp)).toISOString();
                    } catch (error) {
                        return null;
                    }
                }

                async sendToAPI(groupData, isNewGroup) {
                    const maxAttempts = this.settings.retryAttempts;
                    let lastError = null;

                    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
                        try {
                            const payload = {
                                groups: [groupData],
                                metadata: {
                                    exportedAt: new Date().toISOString(),
                                    totalGroups: 1,
                                    totalErrors: 0,
                                    discordUser: UserStore.getCurrentUser().username,
                                    pluginVersion: this.getVersion(),
                                    isNewGroup: isNewGroup,
                                    monitoringMode: true,
                                    attempt: attempt
                                }
                            };

                            // Создаем контроллер для таймаута
                            const controller = new AbortController();
                            const timeoutId = setTimeout(() => controller.abort(), this.settings.requestTimeout * 1000);

                            const response = await fetch(`${this.settings.apiUrl}/api/import/groups`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify(payload),
                                signal: controller.signal
                            });

                            clearTimeout(timeoutId);

                            if (!response.ok) {
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }

                            const result = await response.json();

                            if (isNewGroup) {
                                this.log(`✅ Новая группа успешно добавлена в БД: ${groupData.name} (попытка ${attempt}/${maxAttempts})`);
                            } else {
                                this.log(`🔄 Группа синхронизирована: ${groupData.name} (попытка ${attempt}/${maxAttempts})`);
                            }

                            this.log(`📊 Результат: ${result.importedUsers} пользователей, ${result.importedGroups} групп, ${result.importedConnections} связей`);

                            return result; // Успешно отправлено

                        } catch (error) {
                            lastError = error;

                            if (attempt < maxAttempts) {
                                const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Экспоненциальная задержка, максимум 10 сек
                                this.log(`⚠️ Попытка ${attempt}/${maxAttempts} неудачна для группы ${groupData.name}. Повтор через ${delay/1000} сек. Ошибка: ${error.message}`);
                                await new Promise(resolve => setTimeout(resolve, delay));
                            }
                        }
                    }

                    // Все попытки исчерпаны
                    this.error(`❌ Не удалось отправить данные в API для группы ${groupData.name} после ${maxAttempts} попыток. Последняя ошибка:`, lastError);

                    // Можно добавить в очередь для повторной отправки позже
                    this.addToRetryQueue(groupData, isNewGroup);
                }

                addToRetryQueue(groupData, isNewGroup) {
                    // Простая реализация очереди повтора
                    if (!this.retryQueue) {
                        this.retryQueue = [];
                    }

                    this.retryQueue.push({
                        groupData,
                        isNewGroup,
                        timestamp: Date.now(),
                        attempts: 0
                    });

                    this.log(`📝 Группа ${groupData.name} добавлена в очередь повтора`);

                    // Запускаем обработку очереди, если она еще не запущена
                    if (!this.retryQueueInterval) {
                        this.startRetryQueueProcessor();
                    }
                }

                startRetryQueueProcessor() {
                    this.retryQueueInterval = setInterval(async () => {
                        if (!this.retryQueue || this.retryQueue.length === 0) {
                            return;
                        }

                        const item = this.retryQueue.shift();
                        const timeSinceAdded = Date.now() - item.timestamp;

                        // Повторяем только если прошло достаточно времени (минимум 5 минут)
                        if (timeSinceAdded > 5 * 60 * 1000) {
                            this.log(`🔄 Повторная попытка отправки группы ${item.groupData.name} из очереди`);
                            await this.sendToAPI(item.groupData, item.isNewGroup);
                        } else {
                            // Возвращаем обратно в очередь
                            this.retryQueue.push(item);
                        }
                    }, 60000); // Проверяем каждую минуту
                }

                getSettingsPanel() {
                    const panel = new Settings.SettingPanel();

                    const mainGroup = new Settings.SettingGroup("Основные настройки");

                    mainGroup.append(new Settings.Textbox("API URL", "URL вашего API сервера", this.settings.apiUrl, (value) => {
                        this.settings.apiUrl = value;
                        this.saveSettings();
                        this.log(`⚙️ API URL изменен на: ${value}`);
                    }));

                    mainGroup.append(new Settings.Switch("Включить логирование", "Показывать подробные логи в консоли", this.settings.enableLogging, (value) => {
                        this.settings.enableLogging = value;
                        this.saveSettings();
                        this.log(`⚙️ Логирование ${value ? 'включено' : 'отключено'}`);
                    }));

                    mainGroup.append(new Settings.Switch("Мониторинг в реальном времени", "Отслеживать создание групп и добавление пользователей мгновенно", this.settings.realTimeMonitoring, (value) => {
                        this.settings.realTimeMonitoring = value;
                        this.saveSettings();
                        this.log(`⚙️ Мониторинг в реальном времени ${value ? 'включен' : 'отключен'}`);
                        if (this.isInitialized) {
                            Patcher.unpatchAll();
                            this.patchChannelEvents();
                        }
                    }));



                    const advancedGroup = new Settings.SettingGroup("Расширенные настройки");

                    advancedGroup.append(new Settings.Slider("Попытки повтора при ошибке", "Сколько раз повторять запрос при ошибке API", 1, 10, this.settings.retryAttempts, (value) => {
                        this.settings.retryAttempts = value;
                        this.saveSettings();
                        this.log(`⚙️ Количество попыток повтора изменено на ${value}`);
                    }));

                    advancedGroup.append(new Settings.Slider("Таймаут запроса (секунды)", "Максимальное время ожидания ответа от API", 5, 120, this.settings.requestTimeout, (value) => {
                        this.settings.requestTimeout = value;
                        this.saveSettings();
                        this.log(`⚙️ Таймаут запроса изменен на ${value} секунд`);
                    }));

                    panel.append(mainGroup, advancedGroup);
                    return panel.getElement();
                }

                onSettingChange(id, value) {
                    // Этот метод вызывается автоматически при изменении настроек
                    // Логика обработки уже реализована в коллбэках настроек выше
                }

            };
        };
        return plugin(Plugin, Api);
    })(global.ZeresPluginLibrary.buildPlugin(config));
})();
